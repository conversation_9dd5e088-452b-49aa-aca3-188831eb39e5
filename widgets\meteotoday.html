<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dashboard Météo Marine</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.0/chart.min.js"></script>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
      }

      body {
        background: linear-gradient(135deg, #0e4e74 0%, #05316b 100%);
        max-width: calc(100vw - 450px);
        min-height: 100vh;
        margin: 0;
        padding: 20px;
        color: #e6e6e6;
        overflow-x: hidden;
      }

      .container {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 15px;
        padding: 15px;
        backdrop-filter: blur(10px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.1);
        margin-bottom: 20px;
      }

      h1 {
        font-size: 2.2em;
        padding-bottom: 10px;
        margin: 0 0 10px 0;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
      }

      h2 {
        color: #ffffff;
        font-size: 1rem;
        margin-bottom: 20px;
        font-weight: 500;
        letter-spacing: 0.5px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      .chart-container {
        width: 100%;
        height: 390px;
        background: rgba(255, 255, 255, 0.02);
        border-radius: 12px;
        padding: 10px;
        border: 1px solid rgba(255, 255, 255, 0.05);
      }

      .table-wrapper {
        overflow-x: auto;
        margin-top: 20px;
        border-radius: 12px;
        background: rgba(255, 255, 255, 0.02);
        padding: 2px;
      }

      table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        background: transparent;
      }

      th, td {
        padding: 7px;
        text-align: center;
        border: none;
        min-width: 45px;
        font-size: 0.8rem;
      }

      th {
        background: rgba(255, 255, 255, 0.1);
        color: #ffffff;
        font-weight: 500;
        letter-spacing: 0.5px;
        border-bottom: 2px solid rgba(255, 255, 255, 0.1);
      }

      td {
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
      }

      .row-header {
        text-align: left;
        font-weight: 500;
        color: #ffffff;
        background: rgba(255, 255, 255, 0.05);
        padding-left: 15px;
      }

      .direction-arrow {
        display: inline-block;
        transform-origin: center;
        font-size: 1rem;
        width: 20px;
        height: 20px;
        line-height: 20px;
        color: #ffffff;
      }

      td[style*="background-color"] {
        border-radius: 0px;
        transition: background-color 0.3s ease;
      }

      @media (max-width: 1200px) {
        th, td {
          padding: 6px;
          min-width: 45px;
          font-size: 0.85rem;
        }
        
        h2 {
          font-size: 1.3rem;
        }
      }

      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }

      #windText {
        display: flex;
        justify-content: right;
        padding: 3px;
        color: #ffffff70;
      }

      .table-wrapper {
        animation: fadeIn 0.3s ease-in-out;
      }

      td:hover {
        background: rgba(255, 255, 255, 0.1);
        transition: background 0.2s ease;
      }
    </style>
  </head>
  <body>   
    <h1>Prévisions du Vent : aujourd'hui</h1>

    <div class="container">
      <h2>Vitesse du Vent et Rafales</h2>
      <div class="chart-container">
        <canvas id="windChart"></canvas>
      </div>        
      <p id="windText">Données Météofrance</p>
    </div>

    <div class="container">
      <h2>Données Météo Marines</h2>
      <div class="table-wrapper">
        <table id="weatherTable">
          <thead>
            <tr>
              <th>Heures : </th>
              <th>05h</th><th>06h</th><th>07h</th><th>08h</th><th>09h</th>
              <th>10h</th><th>11h</th><th>12h</th><th>13h</th><th>14h</th>
              <th>15h</th><th>16h</th><th>17h</th><th>18h</th><th>19h</th>
              <th>20h</th><th>21h</th><th>22h</th><th>23h</th>
            </tr>
          </thead>
          <tbody>
            <tr><td class="row-header">Vent (kts)</td></tr>
            <tr><td class="row-header">Rafales (kts)</td></tr>
            <tr><td class="row-header">Direction</td></tr>
            <tr><td class="row-header">Vagues (m)</td></tr>
            <tr><td class="row-header">Période (s)</td></tr>
            <tr><td class="row-header">Dir. Vagues</td></tr>
            <tr><td class="row-header">Temp (°C)</td></tr>
            <tr><td class="row-header">Précip (mm)</td></tr>
          </tbody>
        </table>
      </div>
    </div>

    <script>
      const API_CONFIG = {
        weather: "https://api.open-meteo.com/v1/forecast",
        marine: "https://marine-api.open-meteo.com/v1/marine",
        params: {
          latitude: 49,
          longitude: -1.6,
        },
      };

      function buildWeatherApiUrl() {
        const params = new URLSearchParams({
          latitude: API_CONFIG.params.latitude,
          longitude: API_CONFIG.params.longitude,
          minutely_15: "temperature_2m,precipitation,wind_speed_10m,wind_direction_10m,wind_gusts_10m",
          wind_speed_unit: "kn",
          timezone: "auto",
          past_minutely_15: "1",
          forecast_days: "1",
          forecast_minutely_15: "96",
          models: "meteofrance_seamless",
        });
        return `${API_CONFIG.weather}?${params.toString()}`;
      }

      function buildMarineApiUrl() {
        const params = new URLSearchParams({
          latitude: API_CONFIG.params.latitude,
          longitude: API_CONFIG.params.longitude,
          hourly: "wave_height,wave_direction,wave_period",
          wind_speed_unit: "kn",
          timezone: "auto",
          forecast_days: "1",
          models: "best_match",
        });
        return `${API_CONFIG.marine}?${params.toString()}`;
      }

      function average(arr) {
        return arr.reduce((a, b) => a + b, 0) / arr.length;
      }

      function sum(arr) {
        return arr.reduce((a, b) => a + b, 0);
      }

      function averageAngle(angles) {
        const x = angles.reduce((a, b) => a + Math.cos((b * Math.PI) / 180), 0);
        const y = angles.reduce((a, b) => a + Math.sin((b * Math.PI) / 180), 0);
        return Math.round(((Math.atan2(y, x) * 180) / Math.PI + 360) % 360);
      }

      function aggregateHourlyData(minutelyData) {
        const hourlyData = {};

        minutelyData.time.forEach((time, index) => {
          const hour = new Date(time).getHours();
          if (!hourlyData[hour]) {
            hourlyData[hour] = {
              temp: [],
              precip: [],
              wind: [],
              gusts: [],
              direction: [],
            };
          }

          hourlyData[hour].temp.push(minutelyData.temperature_2m[index]);
          hourlyData[hour].precip.push(minutelyData.precipitation[index]);
          hourlyData[hour].wind.push(minutelyData.wind_speed_10m[index]);
          hourlyData[hour].gusts.push(minutelyData.wind_gusts_10m[index]);
          hourlyData[hour].direction.push(minutelyData.wind_direction_10m[index]);
        });

        for (const hour in hourlyData) {
          hourlyData[hour] = {
            temp: average(hourlyData[hour].temp),
            precip: sum(hourlyData[hour].precip),
            wind: average(hourlyData[hour].wind),
            gusts: Math.max(...hourlyData[hour].gusts),
            direction: averageAngle(hourlyData[hour].direction),
          };
        }

        return hourlyData;
      }

      function createDirectionArrow(degrees, isWind = false) {
        // Ajuster la direction seulement pour le vent
        const adjustedDegrees = isWind ? (degrees + 180) % 360 : degrees;
        return `<span class="direction-arrow" style="transform: rotate(${adjustedDegrees}deg)">↑</span>`;
      }

      function getWindColor(value) {
        if (value === null || value === undefined || value === "---") {
          return "transparent";
        }

        if (value <= 1) return "#E0FFFF";
        if (value <= 3) return "#D7F8FA";
        if (value <= 6) return "#78E9E4";
        if (value <= 10) return "#90EE90";
        if (value <= 16) return "#98FB98";
        if (value <= 21) return "#F0E68C";
        if (value <= 27) return "#FFD700";
        if (value <= 33) return "#FFA500";
        if (value <= 40) return "#FF6B6B";
        if (value <= 47) return "#FF4500";
        if (value <= 55) return "#FF4500";
        if (value <= 63) return "#B50B5E";
        return "#800020";
      }

      function updateTable(weatherData, marineData) {
        const tbody = document.querySelector("#weatherTable tbody");
        const rows = tbody.getElementsByTagName("tr");
        const hours = Array.from({ length: 19 }, (_, i) => i + 5);

        for (let row of rows) {
          while (row.cells.length > 1) {
            row.deleteCell(1);
          }
        }

        hours.forEach((hour) => {
          const weatherHourData = weatherData[hour] || {};
          const marineHourData = marineData.hourly;

          // Vent
          const windCell = rows[0].insertCell();
          const windValue = weatherHourData.wind ? Math.round(weatherHourData.wind) : "---";
          windCell.textContent = windValue;
          windCell.style.backgroundColor = getWindColor(windValue === "---" ? null : windValue);
          windCell.style.color = windValue > 33 ? "white" : "black";

          // Rafales
          const gustCell = rows[1].insertCell();
          const gustValue = weatherHourData.gusts ? Math.round(weatherHourData.gusts) : "---";
          gustCell.textContent = gustValue;
          gustCell.style.backgroundColor = getWindColor(gustValue === "---" ? null : gustValue);
          gustCell.style.color = gustValue > 33 ? "white" : "black";

          // Direction du vent
          const dirCell = rows[2].insertCell();
          dirCell.innerHTML = weatherHourData.direction 
            ? createDirectionArrow(weatherHourData.direction, true) // Notez le true ici
            : "---";

          // Vagues
          const waveCell = rows[3].insertCell();
          waveCell.textContent = marineHourData?.wave_height[hour]?.toFixed(1) || "---";

          // Période
          const periodCell = rows[4].insertCell();
          periodCell.textContent = marineHourData?.wave_period[hour]
            ? Math.round(marineHourData.wave_period[hour])
            : "---";

          // Direction des vagues
          const waveDirCell = rows[5].insertCell();
          waveDirCell.innerHTML = marineHourData?.wave_direction[hour]
            ? createDirectionArrow(marineHourData.wave_direction[hour], false) // Notez le false ici
            : "---";

          // Température
          const tempCell = rows[6].insertCell();
          tempCell.textContent = weatherHourData.temp
            ? Math.round(weatherHourData.temp)
            : "---";

          // Précipitations
          const precipCell = rows[7].insertCell();
          precipCell.textContent = weatherHourData.precip?.toFixed(1) || "---";
        });
      }

      function initChart(hours, windSpeed, windGusts) {
        const ctx = document.getElementById("windChart").getContext("2d");
        const maxValue = Math.max(
          ...windSpeed.filter((v) => v !== null),
          ...windGusts.filter((v) => v !== null)
        );
        const yAxisMax = Math.ceil(maxValue / 10) * 10;
        const stepSize = yAxisMax <= 30 ? 5 : 10;

        return new Chart(ctx, {
          type: "line",
          data: {
            labels: hours.map((h) => `${h}h`),
            datasets: [
              {
                label: "Vent (kts)",
                data: windSpeed.map((v) => Math.round(v)),
                borderColor: "#086AAF",
                borderWidth: 3,
                tension: 0.4,
                fill: false,
                cubicInterpolationMode: "monotone",
                spanGaps: true,
              },
              {
                label: "Rafales (kts)",
                data: windGusts.map((v) => Math.round(v)),
                borderColor: "#011D6B",
                borderWidth: 3,
                tension: 0.4,
                fill: false,
                cubicInterpolationMode: "monotone",
                spanGaps: true,
              },
            ],
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            layout: {
              padding: {
                left: 100,
                right: 10,
                top: 20,
                bottom: 10,
              },
            },
            scales: {
              y: {
                beginAtZero: true,
                max: yAxisMax,
                ticks: {
                  stepSize: stepSize,
                  color: "#ffffff",
                  callback: function (value) {
                    return Math.round(value);
                  },
                },
              },
              x: {
                ticks: {
                  color: "#ffffff",
                }
              }
            },
            plugins: {
              legend: {
                position: "top",
                labels: {
                  padding: 20,
                  color: "#ffffff",
                  font: {
                    size: 14,
                  }
                },
              },
            },
            elements: {
              line: {
                z: 2,
              },
            },
          },
          plugins: [
            {
              id: "custom_canvas_background_color",
              beforeDraw: (chart) => {
                const ctx = chart.canvas.getContext("2d");
                ctx.save();
                ctx.globalCompositeOperation = "destination-over";

                const chartArea = chart.chartArea;
                const gradientBackground = ctx.createLinearGradient(
                  chartArea.left,
                  chartArea.bottom,
                  chartArea.left,
                  chartArea.top
                );

                const beaufortScale = [
                  { force: 0, threshold: 1, color: "#CEFCFC" },
                  { force: 1, threshold: 3, color: "#A3F3F0" },
                  { force: 2, threshold: 6, color: "#78E9E4" },
                  { force: 3, threshold: 10, color: "#77F2AD" },
                  { force: 4, threshold: 16, color: "#75FA75" },
                  { force: 5, threshold: 21, color: "#F0E68C" },
                  { force: 6, threshold: 27, color: "#FFD700" },
                  { force: 7, threshold: 33, color: "#FFA500" },
                  { force: 8, threshold: 40, color: "#FF6B6B" },
                  { force: 9, threshold: 47, color: "#FF4500" },
                  { force: 10, threshold: 55, color: "#FF4500" },
                  { force: 11, threshold: 63, color: "#B50B5E" },
                  { force: 12, threshold: Infinity, color: "#800080" },
                ];

                beaufortScale.forEach((level, index) => {
                  const stopPosition = Math.min(level.threshold / yAxisMax, 1);
                  gradientBackground.addColorStop(stopPosition, level.color);
                });

                ctx.fillStyle = gradientBackground;
                ctx.fillRect(
                  chartArea.left,
                  chartArea.top,
                  chartArea.right - chartArea.left,
                  chartArea.bottom - chartArea.top
                );

                ctx.restore();
              },
            },
          ],
        });
      }

      async function loadData() {
        try {
          const weatherResponse = await fetch(buildWeatherApiUrl());
          const weatherData = await weatherResponse.json();
          const hourlyWeatherData = aggregateHourlyData(
            weatherData.minutely_15
          );

          const marineResponse = await fetch(buildMarineApiUrl());
          const marineData = await marineResponse.json();

          const hours = Array.from({ length: 19 }, (_, i) => i + 5);
          const windSpeed = hours.map(
            (hour) => hourlyWeatherData[hour]?.wind || 0
          );
          const windGusts = hours.map(
            (hour) => hourlyWeatherData[hour]?.gusts || 0
          );

          initChart(hours, windSpeed, windGusts);
          updateTable(hourlyWeatherData, marineData);
        } catch (error) {
          console.error("Erreur lors du chargement des données:", error);
        }
      }

      loadData();
      setInterval(loadData, 15 * 60 * 1000);
    </script>
  </body>
</html>