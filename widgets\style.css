/* Reset et styles de base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: "Segoe UI", system-ui, -apple-system, sans-serif;
}

body {
    background: linear-gradient(135deg, #07285a 0%, #022a50 100%);
    color: #e6e6e6;
    min-height: 100vh;
    margin: 0;
    padding: 20px;
    overflow-x: hidden;
}

/* Container Layouts */
.container {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 20px;
    margin: 20px auto;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    max-width: 1200px;
}

/* Headings */
h1, h2, h3 {
    color: #ffffff;
    font-weight: 400;
    letter-spacing: 1px;
    text-align: center;
    text-shadow: 0 2px 15px rgba(0, 0, 0, 0.2);
    position: relative;
    margin-bottom: 40px;
}

h1 {
    font-size: 1.8rem;
}

h2 {
    font-size: 1.5rem;
}

h1::after, h2::after {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #64b5f6, #2196f3);
    border-radius: 2px;
}

/* Tables */
.table-wrapper {
    overflow-x: auto;
    margin: 20px 0;
}

table {
    width: 100%;
    border-collapse: collapse;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 10px;
}

th, td {
    padding: 12px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

th {
    background: rgba(255, 255, 255, 0.1);
    font-weight: 500;
}

/* Charts */
.chart-container {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 15px;
    padding: 20px;
    margin: 20px 0;
    min-height: 400px;
}

/* Weather Cards Container */
.weather-container-wrapper {
    width: 100%;
    overflow-x: auto;
    padding: 10px 0;
    scrollbar-width: none;
    -ms-overflow-style: none;
    mask-image: linear-gradient(
        90deg,
        transparent,
        #000 5%,
        #000 95%,
        transparent
    );
    -webkit-mask-image: linear-gradient(
        90deg,
        transparent,
        #000 5%,
        #000 95%,
        transparent
    );
}

.weather-container-wrapper::-webkit-scrollbar {
    display: none;
}

.weather-container {
    display: flex;
    gap: 15px;
    padding: 0px;
    width: max-content;
    margin: 0 auto;
}

/* Weather Cards */
.weather-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 20px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    width: 250px;
    flex-shrink: 0;
    animation: fadeIn 0.5s ease-in-out;
}

.weather-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.08);
}

/* Weather Card Components */
.date {
    text-align: center;
    font-size: 1.1rem;
    font-weight: 500;
    color: #ffffff;
    margin-bottom: 20px;
    text-transform: capitalize;
    letter-spacing: 0.5px;
}

.weather-icon {
    text-align: center;
    margin: 20px 0;
}

.weather-icon i {
    font-size: 4em;
    color: #ffffff;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.4);
    background: linear-gradient(135deg, #64b5f6, #2196f3);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.weather-desc {
    text-align: center;
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 20px;
    min-height: 2.4em;
    font-weight: 300;
    letter-spacing: 0.5px;
}

/* Details Sections */
.details {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 15px;
    padding: 20px;
    margin-top: 20px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(5px);
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 12px 0;
    font-size: 0.95rem;
}

.info-label {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 300;
}

.value {
    padding: 4px 12px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.08);
    font-size: 0.9rem;
    letter-spacing: 0.5px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Tides Section */
.tides-section {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 15px;
    padding: 20px;
    margin-top: 20px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.tides-title {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 15px;
    text-align: center;
    font-weight: 400;
    letter-spacing: 0.5px;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }

    .container {
        padding: 15px;
    }

    .weather-card {
        width: 240px;
    }

    .details, .tides-section {
        padding: 15px;
    }
}

/* Hauteur des marées - Classes de couleur */
.height-12-plus { background: linear-gradient(135deg, #081773, #020B6A); }
.height-10-12 { background: linear-gradient(135deg, #0E227C, #081773); }
.height-8-10 { background: linear-gradient(135deg, #2C5AAC, #0E227C); }
.height-6-8 { background: linear-gradient(135deg, #3F7BCA, #2C5AAC); }
.height-4-6 { background: linear-gradient(135deg, #4795D3, #3F7BCA); }
.height-0-4 { background: linear-gradient(135deg, #5EC2F5, #4795D3); }