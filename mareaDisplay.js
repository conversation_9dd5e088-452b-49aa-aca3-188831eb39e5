class MareaDisplay {
    constructor() {
        this.TARGET_LEVEL = 2.25;
        this.dataElement = document.getElementById('marea-data');
        this.TIDE_ICONS = {
            up: './assets/tide-up.svg',
            down: './assets/tide-down.svg'
        };

        if (!this.dataElement) {
            console.error('Element HTML manquant pour MareaDisplay');
            return;
        }

        this.startUpdates();
    }

    async startUpdates() {
        await this.updateDisplay();
        setInterval(() => this.updateDisplay(), 60000);
    }

    findCrossings(data) {
        const crossings = [];
        
        for (let i = 1; i < data.length; i++) {
            const prev = {
                height: parseFloat(data[i-1].height),
                timestamp: data[i-1].timestamp
            };
            const curr = {
                height: parseFloat(data[i].height),
                timestamp: data[i].timestamp
            };

            if ((prev.height - this.TARGET_LEVEL) * (curr.height - this.TARGET_LEVEL) <= 0) {
                const ratio = (this.TARGET_LEVEL - prev.height) / (curr.height - prev.height);
                const timestamp = prev.timestamp + ratio * (curr.timestamp - prev.timestamp);
                const datetime = new Date(timestamp * 1000);
                
                crossings.push({
                    timestamp: timestamp,
                    datetime: datetime,
                    direction: curr.height > prev.height ? 'up' : 'down'
                });
            }
        }
        
        return crossings;
    }

    formatDate(date) {
        // Format de la date
        const dateOptions = { 
            weekday: 'long', 
            day: 'numeric', 
            month: 'long'
        };
        
        // Format de l'heure
        const timeStr = date.toLocaleTimeString('fr-FR', {
            hour: '2-digit',
            minute: '2-digit'
        });

        // Retourne un objet contenant date et heure séparément
        return {
            date: date.toLocaleDateString('fr-FR', dateOptions),
            time: timeStr
        };
    }

    async updateDisplay() {
        try {
            const data = await window.electronAPI.tideAPI.getTidesData();
            
            if (!data || !data.chartData || data.chartData.length === 0) {
                this.dataElement.innerHTML = 'Aucune donnée disponible';
                return;
            }

            const now = Date.now() / 1000;
            const crossings = this.findCrossings(data.chartData)
                .filter(crossing => crossing.timestamp > now)
                .slice(0, 4);

            let html = `
                <div style="margin: 3px; padding-up: 5px;
                display:flex;">
                    <h3>Prochains passages à 5m :</h3>
                </div>
                <div class="crossings-list" style="font-size: 1.1em;">
            `;

            crossings.forEach(crossing => {
                const iconPath = crossing.direction === 'up' 
                    ? this.TIDE_ICONS.up
                    : this.TIDE_ICONS.down;
                
                const formattedDateTime = this.formatDate(crossing.datetime);
                const timeColor = crossing.direction === 'down' ? '#1C70C2' : '#FFFFFF';
                
                html += `
                    <div style="
                        padding: 5px;
                        border-bottom: 1px solid #eee;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    ">
                        <div style="flex: 1;">
                            <div style="font-size: 0.8em;">${formattedDateTime.date}</div>
                            <div style="color: ${timeColor}; font-weight: bold; font-size: 1em;">
                                ${formattedDateTime.time}
                            </div>
                        </div>
                        <img 
                            src="${iconPath}" 
                            alt="${crossing.direction === 'up' ? 'Marée montante' : 'Marée descendante'}"
                            style="width: 30px; height: 100%; margin-left: 15px;"
                        />
                    </div>
                `;
            });

            html += `</div>`;

            if (crossings.length === 0) {
                html = `
                    <div style="text-align: center; padding: 20px;">
                        Aucun passage prévu à 5m dans les prochaines heures
                    </div>
                `;
            }

            this.dataElement.innerHTML = html;

        } catch (error) {
            console.error('Erreur lors de la mise à jour:', error);
            this.dataElement.innerHTML = `Erreur: ${error.message}`;
        }
    }
}

document.addEventListener('DOMContentLoaded', () => {
    const mareaDisplay = new MareaDisplay();
});