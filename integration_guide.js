// Guide d'intégration pour le gestionnaire de playlist amélioré

// 1. Remplacer l'ancien gestionnaire dans votre code existant
function integrateImprovedPlaylistManager() {
    // Remplacez cette ligne :
    // initializePlaylistManager();
    
    // Par celle-ci :
    if (window.playlistManagerImproved) {
        window.playlistManagerImproved.initialize();
    }
}

// 2. Mise à jour des appels de fonctions
function updateFunctionCalls() {
    // Ancien code :
    // const playlist = currentPlaylist;
    
    // Nouveau code :
    const playlist = window.playlistManagerImproved.getCurrentPlaylist();
    
    // Pour définir une playlist :
    window.playlistManagerImproved.setCurrentPlaylist(newPlaylist);
    
    // Pour contrôler l'audio :
    window.playlistManagerImproved.toggleGlobalAudio();
}

// 3. Intégration avec le système de streaming existant
function integrateWithStreaming() {
    // Récupérer la playlist pour le streaming
    const currentPlaylist = window.playlistManagerImproved.getCurrentPlaylist();
    
    // Appliquer les paramètres audio globaux
    function playItem(item) {
        if (item.type === 'video') {
            const video = document.createElement('video');
            video.src = item.content;
            video.autoplay = true;
            
            // Utiliser le paramètre audio global
            video.muted = !globalAudioEnabled; // Variable définie dans playlistManagerImproved.js
            video.controls = true;
            
            // Reste du code...
        }
    }
}

// 4. Gestion des événements personnalisés
function setupCustomEvents() {
    // Écouter les changements de playlist
    document.addEventListener('playlistChanged', function(event) {
        console.log('Playlist modifiée:', event.detail);
        // Mettre à jour votre interface si nécessaire
    });
    
    // Écouter les changements d'audio
    document.addEventListener('audioToggled', function(event) {
        console.log('Audio togglé:', event.detail.enabled);
        // Appliquer aux vidéos en cours de lecture
        document.querySelectorAll('video').forEach(video => {
            video.muted = !event.detail.enabled;
        });
    });
}

// 5. Migration des données existantes
function migrateExistingData() {
    // Si vous avez des playlists existantes dans un autre format
    const oldPlaylists = JSON.parse(localStorage.getItem('oldPlaylistFormat') || '{}');
    
    Object.keys(oldPlaylists).forEach(name => {
        const oldPlaylist = oldPlaylists[name];
        const newPlaylist = oldPlaylist.map(item => ({
            id: Date.now() + Math.random(),
            type: item.type || 'video',
            name: item.name || 'Média sans nom',
            content: item.content || item.url,
            duration: item.duration || 10000,
            size: item.size || 0
        }));
        
        // Sauvegarder dans le nouveau format
        const savedPlaylists = JSON.parse(localStorage.getItem('savedPlaylists') || '{}');
        savedPlaylists[name] = newPlaylist;
        localStorage.setItem('savedPlaylists', JSON.stringify(savedPlaylists));
    });
}

// 6. Configuration personnalisée
const playlistConfig = {
    // Couleurs personnalisées
    colors: {
        primary: '#667eea',
        secondary: '#764ba2',
        success: '#28a745',
        danger: '#dc3545'
    },
    
    // Paramètres par défaut
    defaults: {
        imageDuration: 10000, // 10 secondes
        videoDuration: 0,     // Durée automatique
        audioEnabled: true    // Audio activé par défaut
    },
    
    // Formats supportés
    supportedFormats: {
        video: ['mp4', 'webm', 'ogg', 'avi', 'mov'],
        image: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']
    }
};

// 7. Fonctions utilitaires pour l'intégration
const PlaylistUtils = {
    // Valider un élément de playlist
    validatePlaylistItem(item) {
        return item && 
               item.type && 
               item.name && 
               item.content && 
               typeof item.duration === 'number';
    },
    
    // Convertir une durée en format lisible
    formatDuration(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    },
    
    // Obtenir l'icône pour un type de média
    getMediaIcon(type) {
        const icons = {
            'image': 'fas fa-image',
            'video': 'fas fa-video',
            'webpage': 'fas fa-globe'
        };
        return icons[type] || 'fas fa-file';
    },
    
    // Calculer la taille totale d'une playlist
    calculatePlaylistSize(playlist) {
        return playlist.reduce((total, item) => total + (item.size || 0), 0);
    },
    
    // Exporter une playlist au format JSON
    exportPlaylist(playlist, name) {
        const exportData = {
            name: name,
            created: new Date().toISOString(),
            version: '2.0',
            items: playlist
        };
        
        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${name}.json`;
        a.click();
        URL.revokeObjectURL(url);
    },
    
    // Importer une playlist depuis un fichier JSON
    importPlaylist(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);
                    if (data.items && Array.isArray(data.items)) {
                        resolve(data);
                    } else {
                        reject(new Error('Format de fichier invalide'));
                    }
                } catch (error) {
                    reject(error);
                }
            };
            reader.readAsText(file);
        });
    }
};

// 8. Exemple d'utilisation complète
function exampleUsage() {
    // Initialiser le gestionnaire
    window.playlistManagerImproved.initialize();
    
    // Ajouter des éléments programmatiquement
    const newItem = {
        id: Date.now(),
        type: 'video',
        name: 'Ma vidéo.mp4',
        content: 'path/to/video.mp4',
        duration: 120000, // 2 minutes
        size: 1024000     // 1 MB
    };
    
    const currentPlaylist = window.playlistManagerImproved.getCurrentPlaylist();
    currentPlaylist.push(newItem);
    window.playlistManagerImproved.setCurrentPlaylist(currentPlaylist);
    
    // Afficher une notification
    window.playlistManagerImproved.showNotification('Vidéo ajoutée !', 'success');
}

// Export pour utilisation dans d'autres modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        integrateImprovedPlaylistManager,
        updateFunctionCalls,
        integrateWithStreaming,
        setupCustomEvents,
        migrateExistingData,
        playlistConfig,
        PlaylistUtils,
        exampleUsage
    };
}
