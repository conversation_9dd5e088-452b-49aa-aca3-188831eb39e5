<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Guide d'utilisation - NAUTIFLIX</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
        }
        h2 {
            color: #0099ff;
            margin-top: 30px;
        }
        .tip {
            background-color: #e6f3ff;
            border-left: 5px solid #0099ff;
            padding: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>Guide de prise en main - NAUTIFLIX</h1>
    <p>Club Nautique de Coutainville</p>

    <h2>1. Démarrage de l'application</h2>
    <ul>
        <li>Double-cliquez sur l'icône de l'application pour la lancer.</li>
        <li>L'interface principale s'affichera avec le titre "NAUTIFLIX - Club Nautique de Coutainville".</li>
    </ul>

    <h2>2. Interface principale</h2>
    <ul>
        <li>En haut de l'écran, vous trouverez des sections pour les informations générales et les marées.</li>
        <li>Le centre de l'écran est dédié à l'affichage du contenu multimédia.</li>
    </ul>

    <h2>3. Contrôles de lecture</h2>
    <ul>
        <li>Utilisez les boutons DEMARRER et STOP pour lancer ou arrêter la lecture de la playlist.</li>
        <li>Les boutons ◀ et ▶ permettent de naviguer entre les éléments de la playlist.</li>
    </ul>

    <h2>4. Affichage</h2>
    <ul>
        <li>Cliquez sur le bouton ⛶ pour basculer en mode plein écran.</li>
        <li>Utilisez "Afficher Infos" pour voir ou masquer les informations supplémentaires.</li>
        <li>Le bouton "Bgrd" permet de changer l'arrière-plan de l'application.</li>
    </ul>

    <h2>5. Gestion des playlists</h2>
    <ul>
        <li>Cliquez sur le bouton "Gestion des Playlists" pour ouvrir le menu de gestion.</li>
        <li>Vous pouvez charger une playlist existante, sauvegarder la playlist actuelle ou en supprimer une.</li>
    </ul>

    <h2>6. Ajouter du contenu à la playlist</h2>
    <ul>
        <li>Dans le menu de gestion des playlists, utilisez le bouton "Ajouter un élément".</li>
        <li>Choisissez le type de contenu : Image, Vidéo, Page Web ou iFrame.</li>
        <li>Spécifiez la durée d'affichage en secondes.</li>
        <li>Cliquez sur "Ajouter" pour inclure l'élément dans la playlist.</li>
    </ul>

    <h2>7. Menu de l'application</h2>
    <ul>
        <li>Le menu "Fichier" permet de quitter l'application ou de la recharger.</li>
        <li>Vous pouvez également mettre à jour les données des marées via le sous-menu "Options".</li>
        <li>Le menu "Vue" permet d'ouvrir les outils de développement (pour les utilisateurs avancés).</li>
        <li>Le menu "Aide" contient des informations sur l'application.</li>
    </ul>

    <h2>8. Mise à jour des marées</h2>
    <ul>
        <li>Allez dans Fichier > Options > Mettre à jour les marées pour actualiser les informations de marées.</li>
        <li>La base de données des marées est mise a jour à partir d'une base  hebergée sur Jsonbin.io.  </li>
    </ul>

    <h2>9. Mode plein écran</h2>
    <ul>
        <li>En mode plein écran, le menu de l'application est masqué pour une meilleure visibilité.</li>
        <li>Appuyez sur la touche Echap pour quitter le mode plein écran.</li>
    </ul>

    <h2>10. Fermeture de l'application</h2>
    <ul>
        <li>Utilisez le menu Fichier > Quitter ou le raccourci Ctrl+Q pour fermer l'application.</li>
    </ul>

    <div class="tip">
        <h3>Conseils :</h3>
        <ul>
            <li>Assurez-vous de sauvegarder régulièrement votre playlist si vous y apportez des modifications.</li>
            <li>Mettez à jour les données des marées régulièrement pour avoir les informations les plus récentes.</li>
            <li>Expérimentez avec différents types de contenu dans votre playlist pour créer une présentation dynamique.</li>
        </ul>
    </div>

    <p>Pour toute question supplémentaire ou assistance, n'hésitez pas à consulter la section "Aide" de l'application ou à contacter le support technique du Club Nautique de Coutainville.</p>
</body>
</html>