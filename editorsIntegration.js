// Intégration des Éditeurs dans Nautiflix
// Ce fichier connecte les éditeurs au système de playlist existant

class NautiflixEditorsIntegration {
    constructor() {
        this.meteoEditor = null;
        this.infoEditor = null;
        this.isInitialized = false;
    }

    // Initialiser l'intégration des éditeurs
    initialize() {
        if (this.isInitialized) return;

        this.addEditorsToPlaylistManager();
        this.setupEditorButtons();
        this.registerCustomContentTypes();
        this.isInitialized = true;

        console.log('Éditeurs Nautiflix initialisés avec succès');
    }

    // Ajouter les éditeurs au gestionnaire de playlist
    addEditorsToPlaylistManager() {
        // Étendre le gestionnaire de playlist existant
        if (window.playlistManagerImproved) {
            // Ajouter les nouveaux types de contenu
            window.playlistManagerImproved.addCustomContentType = this.addCustomContentType.bind(this);
            window.playlistManagerImproved.openMeteoEditor = this.openMeteoEditor.bind(this);
            window.playlistManagerImproved.openInfoEditor = this.openInfoEditor.bind(this);
        }

        // Modifier l'interface de playlist pour inclure les éditeurs
        this.addEditorButtonsToPlaylist();
    }

    // Ajouter les boutons d'éditeurs à l'interface de playlist
    addEditorButtonsToPlaylist() {
        // Attendre que l'interface de playlist soit chargée
        const checkPlaylistInterface = () => {
            const playlistActions = document.querySelector('.playlist-actions');
            if (playlistActions && !document.getElementById('editors-section')) {
                this.injectEditorButtons(playlistActions);
            } else {
                setTimeout(checkPlaylistInterface, 500);
            }
        };
        checkPlaylistInterface();
    }

    injectEditorButtons(playlistActions) {
        const editorsSection = document.createElement('div');
        editorsSection.id = 'editors-section';
        editorsSection.className = 'playlist-section';
        editorsSection.innerHTML = `
            <div class="section-header">
                <h3><i class="fas fa-magic"></i> Éditeurs Intégrés</h3>
            </div>
            <div class="editors-buttons">
                <button id="open-meteo-editor" class="editor-btn meteo-btn">
                    <i class="fas fa-cloud-sun"></i>
                    <span>Créer Écran Météo</span>
                </button>
                <button id="open-info-editor" class="editor-btn info-btn">
                    <i class="fas fa-file-alt"></i>
                    <span>Créer Page Info</span>
                </button>
            </div>
            <div class="editors-help">
                <small><i class="fas fa-info-circle"></i> Créez du contenu personnalisé sans modifier de fichiers HTML</small>
            </div>
        `;

        // Insérer après les actions existantes
        playlistActions.parentNode.insertBefore(editorsSection, playlistActions.nextSibling);

        // Ajouter les styles
        this.addEditorButtonsStyles();

        // Ajouter les événements
        document.getElementById('open-meteo-editor').addEventListener('click', () => {
            this.openMeteoEditor();
        });

        document.getElementById('open-info-editor').addEventListener('click', () => {
            this.openInfoEditor();
        });
    }

    addEditorButtonsStyles() {
        if (document.getElementById('editors-integration-styles')) return;

        const style = document.createElement('style');
        style.id = 'editors-integration-styles';
        style.textContent = `
            .playlist-section {
                margin: 20px 0;
                padding: 15px;
                background: #f8f9fa;
                border-radius: 8px;
                border: 1px solid #e9ecef;
            }

            .section-header h3 {
                color: #2c5aa0;
                margin-bottom: 15px;
                font-size: 1.1em;
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .editors-buttons {
                display: flex;
                gap: 10px;
                margin-bottom: 10px;
            }

            .editor-btn {
                flex: 1;
                padding: 12px 15px;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                font-size: 14px;
                transition: all 0.3s ease;
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 5px;
                color: white;
            }

            .meteo-btn {
                background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            }

            .info-btn {
                background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            }

            .editor-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            }

            .editor-btn i {
                font-size: 18px;
            }

            .editors-help {
                text-align: center;
                color: #6c757d;
                font-style: italic;
            }

            .editors-help i {
                margin-right: 5px;
            }

            @media (max-width: 768px) {
                .editors-buttons {
                    flex-direction: column;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // Ouvrir l'éditeur météo
    openMeteoEditor() {
        if (this.meteoEditor) {
            this.meteoEditor.closeEditor();
        }

        this.meteoEditor = new MeteoEditor();
        const editorHTML = this.meteoEditor.createEditor();
        
        const editorContainer = document.createElement('div');
        editorContainer.innerHTML = editorHTML;
        document.body.appendChild(editorContainer.firstElementChild);
        
        this.meteoEditor.initializeEditor();

        // Personnaliser pour l'intégration Nautiflix
        this.customizeMeteoEditor();
    }

    // Ouvrir l'éditeur de pages d'information
    openInfoEditor() {
        if (this.infoEditor) {
            this.infoEditor.closeEditor();
        }

        this.infoEditor = new InfoPageEditor();
        const editorHTML = this.infoEditor.createEditor();
        
        const editorContainer = document.createElement('div');
        editorContainer.innerHTML = editorHTML;
        document.body.appendChild(editorContainer.firstElementChild);
        
        this.infoEditor.initializeEditor();

        // Personnaliser pour l'intégration Nautiflix
        this.customizeInfoEditor();
    }

    // Personnaliser l'éditeur météo pour Nautiflix
    customizeMeteoEditor() {
        // Surcharger la méthode de sauvegarde pour l'intégrer à la playlist
        const originalSave = this.meteoEditor.saveConfiguration;
        this.meteoEditor.saveConfiguration = () => {
            const name = prompt('Nom de la configuration météo:');
            if (!name) return;

            const config = {
                name,
                type: 'meteo-custom',
                content: this.meteoEditor.generateMeteoHTML(),
                duration: 30000, // 30 secondes par défaut
                config: this.meteoEditor.currentConfig,
                created: new Date().toISOString()
            };

            // Sauvegarder dans localStorage
            const savedConfigs = JSON.parse(localStorage.getItem('meteoConfigs') || '{}');
            savedConfigs[name] = config;
            localStorage.setItem('meteoConfigs', JSON.stringify(savedConfigs));

            // Ajouter directement à la playlist actuelle
            this.addToCurrentPlaylist(config);

            // Fermer l'éditeur
            this.meteoEditor.closeEditor();

            // Notification
            this.showNotification(`Configuration météo "${name}" ajoutée à la playlist !`, 'success');
        };
    }

    // Personnaliser l'éditeur de pages pour Nautiflix
    customizeInfoEditor() {
        // Surcharger la méthode de sauvegarde
        const originalSave = this.infoEditor.saveInfoPage;
        this.infoEditor.saveInfoPage = () => {
            const name = this.infoEditor.currentPage.title || prompt('Nom de la page d\'information:');
            if (!name) return;

            const config = {
                name,
                type: 'info-page',
                content: this.infoEditor.generatePageHTML(),
                duration: 60000, // 1 minute par défaut
                config: this.infoEditor.currentPage,
                created: new Date().toISOString()
            };

            // Sauvegarder dans localStorage
            const savedPages = JSON.parse(localStorage.getItem('infoPages') || '{}');
            savedPages[name] = config;
            localStorage.setItem('infoPages', JSON.stringify(savedPages));

            // Ajouter directement à la playlist actuelle
            this.addToCurrentPlaylist(config);

            // Fermer l'éditeur
            this.infoEditor.closeEditor();

            // Notification
            this.showNotification(`Page "${name}" ajoutée à la playlist !`, 'success');
        };
    }

    // Ajouter un élément à la playlist actuelle
    addToCurrentPlaylist(item) {
        // Vérifier si on utilise le gestionnaire amélioré
        if (window.playlistManagerImproved && window.playlistManagerImproved.getCurrentPlaylist) {
            const currentPlaylist = window.playlistManagerImproved.getCurrentPlaylist();
            currentPlaylist.push(item);
            window.playlistManagerImproved.setCurrentPlaylist(currentPlaylist);
        } 
        // Sinon utiliser le système original
        else if (window.currentPlaylist) {
            window.currentPlaylist.push(item);
            if (typeof updatePlaylistDisplay === 'function') {
                updatePlaylistDisplay();
            }
        }
    }

    // Enregistrer les nouveaux types de contenu
    registerCustomContentTypes() {
        // Étendre la fonction playItem pour supporter les nouveaux types
        if (window.playItem) {
            const originalPlayItem = window.playItem;
            window.playItem = (item) => {
                if (item.type === 'meteo-custom' || item.type === 'info-page') {
                    this.playCustomContent(item);
                } else {
                    originalPlayItem(item);
                }
            };
        }
    }

    // Lire le contenu personnalisé
    playCustomContent(item) {
        const currentContent = document.getElementById('stream-content-1');
        const nextContent = document.getElementById('stream-content-2');
        
        nextContent.innerHTML = item.content;
        
        // Appliquer les styles spécifiques
        if (item.type === 'meteo-custom') {
            nextContent.classList.add('meteo-display');
        } else if (item.type === 'info-page') {
            nextContent.classList.add('info-page-display');
        }
        
        // Transition
        currentContent.style.opacity = '0';
        nextContent.style.opacity = '1';
        
        setTimeout(() => {
            const temp = currentContent.id;
            currentContent.id = nextContent.id;
            nextContent.id = temp;
        }, 1000);
        
        // Démarrer le timer
        if (typeof startTimer === 'function') {
            startTimer(item.duration);
        }
    }

    // Ajouter un type de contenu personnalisé
    addCustomContentType(config) {
        this.addToCurrentPlaylist(config);
    }

    // Configurer les boutons d'éditeurs dans l'interface principale
    setupEditorButtons() {
        // Ajouter des boutons dans la zone de contrôle si nécessaire
        const controlArea = document.getElementById('control-area');
        if (controlArea && !document.getElementById('editors-control-btn')) {
            const editorsBtn = document.createElement('button');
            editorsBtn.id = 'editors-control-btn';
            editorsBtn.className = 'control-button';
            editorsBtn.innerHTML = '<i class="fas fa-magic"></i>';
            editorsBtn.title = 'Éditeurs de Contenu';
            
            editorsBtn.addEventListener('click', () => {
                this.showEditorsMenu();
            });
            
            controlArea.appendChild(editorsBtn);
        }
    }

    // Afficher le menu des éditeurs
    showEditorsMenu() {
        const menu = document.createElement('div');
        menu.className = 'editors-menu';
        menu.innerHTML = `
            <div class="menu-content">
                <h3>Éditeurs de Contenu</h3>
                <button onclick="nautiflixEditors.openMeteoEditor()">
                    <i class="fas fa-cloud-sun"></i> Éditeur Météo
                </button>
                <button onclick="nautiflixEditors.openInfoEditor()">
                    <i class="fas fa-file-alt"></i> Éditeur Pages
                </button>
                <button onclick="this.parentElement.parentElement.remove()">
                    Fermer
                </button>
            </div>
        `;
        
        document.body.appendChild(menu);
        
        // Auto-suppression après 5 secondes
        setTimeout(() => {
            if (menu.parentNode) {
                menu.parentNode.removeChild(menu);
            }
        }, 5000);
    }

    // Afficher une notification
    showNotification(message, type = 'info') {
        if (window.playlistManagerImproved && window.playlistManagerImproved.showNotification) {
            window.playlistManagerImproved.showNotification(message, type);
        } else {
            // Fallback simple
            alert(message);
        }
    }

    // Charger les contenus sauvegardés au démarrage
    loadSavedContent() {
        const meteoConfigs = JSON.parse(localStorage.getItem('meteoConfigs') || '{}');
        const infoPages = JSON.parse(localStorage.getItem('infoPages') || '{}');
        
        console.log(`Chargé ${Object.keys(meteoConfigs).length} configurations météo`);
        console.log(`Chargé ${Object.keys(infoPages).length} pages d'information`);
        
        return {
            meteoConfigs,
            infoPages
        };
    }

    // Exporter une configuration
    exportConfig(type, name) {
        const configs = type === 'meteo' 
            ? JSON.parse(localStorage.getItem('meteoConfigs') || '{}')
            : JSON.parse(localStorage.getItem('infoPages') || '{}');
            
        const config = configs[name];
        if (!config) return;
        
        const blob = new Blob([JSON.stringify(config, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${name}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }

    // Importer une configuration
    importConfig(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const config = JSON.parse(e.target.result);
                    
                    if (config.type === 'meteo-custom') {
                        const meteoConfigs = JSON.parse(localStorage.getItem('meteoConfigs') || '{}');
                        meteoConfigs[config.name] = config;
                        localStorage.setItem('meteoConfigs', JSON.stringify(meteoConfigs));
                    } else if (config.type === 'info-page') {
                        const infoPages = JSON.parse(localStorage.getItem('infoPages') || '{}');
                        infoPages[config.name] = config;
                        localStorage.setItem('infoPages', JSON.stringify(infoPages));
                    }
                    
                    resolve(config);
                } catch (error) {
                    reject(error);
                }
            };
            reader.readAsText(file);
        });
    }
}

// Initialiser l'intégration
const nautiflixEditors = new NautiflixEditorsIntegration();

// Auto-initialisation quand le DOM est prêt
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        nautiflixEditors.initialize();
    });
} else {
    nautiflixEditors.initialize();
}

// Export global
window.nautiflixEditors = nautiflixEditors;
