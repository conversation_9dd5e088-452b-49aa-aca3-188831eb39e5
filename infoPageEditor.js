// Éditeur de Pages d'Information pour Nautiflix
// Permet de créer des pages d'information personnalisées (horaires, annonces, etc.)

class InfoPageEditor {
    constructor() {
        this.currentPage = {
            title: '',
            type: 'horaires', // 'horaires', 'annonces', 'evenements', 'custom'
            blocks: [],
            style: {
                theme: 'nautique',
                layout: 'standard',
                colors: {
                    primary: '#2c5aa0',
                    secondary: '#87ceeb',
                    text: '#333333'
                }
            }
        };
        this.blockTypes = this.initializeBlockTypes();
    }

    initializeBlockTypes() {
        return {
            title: {
                name: 'Titre',
                icon: 'fas fa-heading',
                template: '<h2 class="page-title">Titre de la page</h2>',
                editable: true
            },
            text: {
                name: 'Texte',
                icon: 'fas fa-paragraph',
                template: '<p class="text-block">Votre texte ici...</p>',
                editable: true
            },
            schedule: {
                name: '<PERSON><PERSON><PERSON>',
                icon: 'fas fa-clock',
                template: this.getScheduleTemplate(),
                editable: true
            },
            table: {
                name: 'Tableau',
                icon: 'fas fa-table',
                template: this.getTableTemplate(),
                editable: true
            },
            announcement: {
                name: 'Annonce',
                icon: 'fas fa-bullhorn',
                template: this.getAnnouncementTemplate(),
                editable: true
            },
            image: {
                name: 'Image',
                icon: 'fas fa-image',
                template: '<div class="image-block"><img src="" alt="Image"><p class="image-caption">Légende</p></div>',
                editable: true
            },
            contact: {
                name: 'Contact',
                icon: 'fas fa-phone',
                template: this.getContactTemplate(),
                editable: true
            },
            separator: {
                name: 'Séparateur',
                icon: 'fas fa-minus',
                template: '<hr class="separator">',
                editable: false
            }
        };
    }

    getScheduleTemplate() {
        return `
            <div class="schedule-block">
                <h3>Horaires des Cours</h3>
                <table class="schedule-table">
                    <thead>
                        <tr>
                            <th>Jour</th>
                            <th>Activité</th>
                            <th>Horaire</th>
                            <th>Niveau</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Lundi</td>
                            <td>Voile</td>
                            <td>14h00 - 17h00</td>
                            <td>Débutant</td>
                        </tr>
                        <tr>
                            <td>Mercredi</td>
                            <td>Catamaran</td>
                            <td>10h00 - 12h00</td>
                            <td>Confirmé</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        `;
    }

    getTableTemplate() {
        return `
            <div class="table-block">
                <table class="info-table">
                    <thead>
                        <tr>
                            <th>Colonne 1</th>
                            <th>Colonne 2</th>
                            <th>Colonne 3</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Donnée 1</td>
                            <td>Donnée 2</td>
                            <td>Donnée 3</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        `;
    }

    getAnnouncementTemplate() {
        return `
            <div class="announcement-block">
                <div class="announcement-header">
                    <i class="fas fa-exclamation-circle"></i>
                    <h3>Annonce Importante</h3>
                </div>
                <div class="announcement-content">
                    <p>Votre annonce ici...</p>
                </div>
                <div class="announcement-date">
                    <small>Publié le: ${new Date().toLocaleDateString()}</small>
                </div>
            </div>
        `;
    }

    getContactTemplate() {
        return `
            <div class="contact-block">
                <h3>Informations de Contact</h3>
                <div class="contact-info">
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <span>02 33 XX XX XX</span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>Port de Coutainville</span>
                    </div>
                </div>
            </div>
        `;
    }

    createEditor() {
        return `
            <div class="info-editor-container">
                <div class="editor-header">
                    <h2><i class="fas fa-edit"></i> Éditeur de Pages d'Information</h2>
                    <div class="header-controls">
                        <button id="preview-info-page" class="btn-preview">
                            <i class="fas fa-eye"></i> Aperçu
                        </button>
                        <button id="save-info-page" class="btn-save">
                            <i class="fas fa-save"></i> Sauvegarder
                        </button>
                        <button id="close-info-editor" class="btn-close">&times;</button>
                    </div>
                </div>

                <div class="editor-content">
                    <!-- Panneau de configuration -->
                    <div class="page-config-panel">
                        <div class="config-section">
                            <h3><i class="fas fa-cog"></i> Configuration de la Page</h3>
                            
                            <div class="config-group">
                                <label>Titre de la page :</label>
                                <input type="text" id="page-title" class="config-input" placeholder="Titre de votre page">
                            </div>

                            <div class="config-group">
                                <label>Type de page :</label>
                                <select id="page-type" class="config-select">
                                    <option value="horaires">Horaires</option>
                                    <option value="annonces">Annonces</option>
                                    <option value="evenements">Événements</option>
                                    <option value="custom">Personnalisé</option>
                                </select>
                            </div>

                            <div class="config-group">
                                <label>Mise en page :</label>
                                <select id="page-layout" class="config-select">
                                    <option value="standard">Standard</option>
                                    <option value="compact">Compact</option>
                                    <option value="large">Large</option>
                                </select>
                            </div>
                        </div>

                        <div class="config-section">
                            <h3><i class="fas fa-palette"></i> Style</h3>
                            <div class="color-picker-group">
                                <div class="color-input">
                                    <label>Couleur principale :</label>
                                    <input type="color" id="page-primary-color" value="#2c5aa0">
                                </div>
                                <div class="color-input">
                                    <label>Couleur secondaire :</label>
                                    <input type="color" id="page-secondary-color" value="#87ceeb">
                                </div>
                                <div class="color-input">
                                    <label>Couleur du texte :</label>
                                    <input type="color" id="page-text-color" value="#333333">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Panneau des blocs disponibles -->
                    <div class="blocks-panel">
                        <h3><i class="fas fa-th-large"></i> Blocs Disponibles</h3>
                        <div class="blocks-grid" id="available-blocks">
                            <!-- Généré dynamiquement -->
                        </div>
                    </div>

                    <!-- Zone de construction de la page -->
                    <div class="page-builder-panel">
                        <h3><i class="fas fa-file-alt"></i> Construction de la Page</h3>
                        <div class="page-drop-zone" id="page-drop-zone">
                            <div class="drop-zone-content">
                                <i class="fas fa-plus-circle"></i>
                                <p>Glissez les blocs ici pour construire votre page</p>
                            </div>
                            <div class="page-builder" id="page-builder">
                                <!-- Blocs ajoutés apparaîtront ici -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Panneau de prévisualisation -->
                <div class="info-preview-panel" id="info-preview" style="display: none;">
                    <div class="preview-header">
                        <h3><i class="fas fa-eye"></i> Aperçu de la Page</h3>
                        <button id="close-info-preview" class="btn-close">&times;</button>
                    </div>
                    <div class="preview-content" id="info-preview-content">
                        <!-- Aperçu généré ici -->
                    </div>
                </div>

                <!-- Éditeur de contenu modal -->
                <div class="content-editor-modal" id="content-editor-modal" style="display: none;">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>Éditer le Contenu</h3>
                            <button class="close-modal">&times;</button>
                        </div>
                        <div class="modal-body">
                            <div id="rich-text-editor"></div>
                        </div>
                        <div class="modal-footer">
                            <button id="cancel-edit" class="btn-cancel">Annuler</button>
                            <button id="save-edit" class="btn-save">Sauvegarder</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    initializeEditor() {
        // Générer les blocs disponibles
        this.generateAvailableBlocks();
        
        // Initialiser les événements
        this.setupEventListeners();
        
        // Charger la configuration par défaut
        this.loadDefaultConfig();
    }

    generateAvailableBlocks() {
        const container = document.getElementById('available-blocks');
        container.innerHTML = '';

        Object.entries(this.blockTypes).forEach(([key, block]) => {
            const blockElement = document.createElement('div');
            blockElement.className = 'block-item';
            blockElement.draggable = true;
            blockElement.dataset.blockType = key;
            
            blockElement.innerHTML = `
                <div class="block-icon">
                    <i class="${block.icon}"></i>
                </div>
                <div class="block-info">
                    <span class="block-name">${block.name}</span>
                </div>
            `;

            // Événements drag & drop
            blockElement.addEventListener('dragstart', (e) => {
                e.dataTransfer.setData('text/plain', key);
            });

            container.appendChild(blockElement);
        });
    }

    setupEventListeners() {
        // Gestion du drop zone
        const dropZone = document.getElementById('page-drop-zone');
        const pageBuilder = document.getElementById('page-builder');

        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('drag-over');
        });

        dropZone.addEventListener('dragleave', () => {
            dropZone.classList.remove('drag-over');
        });

        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('drag-over');
            
            const blockType = e.dataTransfer.getData('text/plain');
            this.addBlockToPage(blockType);
        });

        // Boutons de contrôle
        document.getElementById('preview-info-page').addEventListener('click', () => {
            this.showPreview();
        });

        document.getElementById('save-info-page').addEventListener('click', () => {
            this.saveInfoPage();
        });

        document.getElementById('close-info-editor').addEventListener('click', () => {
            this.closeEditor();
        });

        // Changements de configuration
        document.getElementById('page-title').addEventListener('input', (e) => {
            this.currentPage.title = e.target.value;
            this.updatePreview();
        });

        document.getElementById('page-type').addEventListener('change', (e) => {
            this.updatePageType(e.target.value);
        });

        // Changements de couleurs
        ['page-primary-color', 'page-secondary-color', 'page-text-color'].forEach(id => {
            document.getElementById(id).addEventListener('change', () => {
                this.updatePageColors();
            });
        });
    }

    addBlockToPage(blockType) {
        const block = this.blockTypes[blockType];
        if (!block) return;

        const blockId = `block-${Date.now()}`;
        const pageBuilder = document.getElementById('page-builder');

        // Masquer le message de drop zone si c'est le premier bloc
        const dropZoneContent = document.querySelector('.drop-zone-content');
        if (dropZoneContent) {
            dropZoneContent.style.display = 'none';
        }

        const blockElement = document.createElement('div');
        blockElement.className = 'page-block';
        blockElement.dataset.blockId = blockId;
        blockElement.dataset.blockType = blockType;

        blockElement.innerHTML = `
            <div class="block-header">
                <span class="block-title">
                    <i class="${block.icon}"></i>
                    ${block.name}
                </span>
                <div class="block-controls">
                    <button class="block-move-up" title="Monter">
                        <i class="fas fa-arrow-up"></i>
                    </button>
                    <button class="block-move-down" title="Descendre">
                        <i class="fas fa-arrow-down"></i>
                    </button>
                    ${block.editable ? '<button class="block-edit" title="Éditer"><i class="fas fa-edit"></i></button>' : ''}
                    <button class="block-remove" title="Supprimer">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="block-content">
                ${block.template}
            </div>
        `;

        // Événements pour ce bloc
        blockElement.querySelector('.block-remove').addEventListener('click', () => {
            this.removeBlock(blockId);
        });

        blockElement.querySelector('.block-move-up').addEventListener('click', () => {
            this.moveBlock(blockId, 'up');
        });

        blockElement.querySelector('.block-move-down').addEventListener('click', () => {
            this.moveBlock(blockId, 'down');
        });

        if (block.editable) {
            blockElement.querySelector('.block-edit').addEventListener('click', () => {
                this.editBlock(blockId, blockType);
            });
        }

        pageBuilder.appendChild(blockElement);

        // Ajouter à la configuration
        this.currentPage.blocks.push({
            id: blockId,
            type: blockType,
            content: block.template,
            order: this.currentPage.blocks.length
        });

        this.updatePreview();
    }

    removeBlock(blockId) {
        // Supprimer de l'interface
        const blockElement = document.querySelector(`[data-block-id="${blockId}"]`);
        if (blockElement) {
            blockElement.remove();
        }

        // Supprimer de la configuration
        this.currentPage.blocks = this.currentPage.blocks.filter(b => b.id !== blockId);

        // Réafficher le message si plus de blocs
        const pageBuilder = document.getElementById('page-builder');
        if (pageBuilder.children.length === 0) {
            document.querySelector('.drop-zone-content').style.display = 'block';
        }

        this.updatePreview();
    }

    moveBlock(blockId, direction) {
        const pageBuilder = document.getElementById('page-builder');
        const blockElement = document.querySelector(`[data-block-id="${blockId}"]`);

        if (direction === 'up' && blockElement.previousElementSibling) {
            pageBuilder.insertBefore(blockElement, blockElement.previousElementSibling);
        } else if (direction === 'down' && blockElement.nextElementSibling) {
            pageBuilder.insertBefore(blockElement.nextElementSibling, blockElement);
        }

        // Mettre à jour l'ordre dans la configuration
        this.updateBlockOrder();
        this.updatePreview();
    }

    updateBlockOrder() {
        const pageBuilder = document.getElementById('page-builder');
        const blockElements = pageBuilder.querySelectorAll('.page-block');

        blockElements.forEach((element, index) => {
            const blockId = element.dataset.blockId;
            const block = this.currentPage.blocks.find(b => b.id === blockId);
            if (block) {
                block.order = index;
            }
        });

        // Trier les blocs par ordre
        this.currentPage.blocks.sort((a, b) => a.order - b.order);
    }

    editBlock(blockId, blockType) {
        const block = this.currentPage.blocks.find(b => b.id === blockId);
        if (!block) return;

        // Ouvrir l'éditeur de contenu
        this.openContentEditor(block);
    }

    openContentEditor(block) {
        const modal = document.getElementById('content-editor-modal');
        const editor = document.getElementById('rich-text-editor');

        // Initialiser l'éditeur de texte riche (simulation)
        editor.innerHTML = `
            <div class="editor-toolbar">
                <button class="editor-btn" data-command="bold"><i class="fas fa-bold"></i></button>
                <button class="editor-btn" data-command="italic"><i class="fas fa-italic"></i></button>
                <button class="editor-btn" data-command="underline"><i class="fas fa-underline"></i></button>
                <button class="editor-btn" data-command="insertOrderedList"><i class="fas fa-list-ol"></i></button>
                <button class="editor-btn" data-command="insertUnorderedList"><i class="fas fa-list-ul"></i></button>
            </div>
            <div class="editor-content" contenteditable="true">
                ${block.content}
            </div>
        `;

        // Événements de l'éditeur
        editor.querySelectorAll('.editor-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const command = btn.dataset.command;
                document.execCommand(command, false, null);
            });
        });

        modal.style.display = 'block';

        // Événements du modal
        modal.querySelector('.close-modal').addEventListener('click', () => {
            modal.style.display = 'none';
        });

        modal.querySelector('#cancel-edit').addEventListener('click', () => {
            modal.style.display = 'none';
        });

        modal.querySelector('#save-edit').addEventListener('click', () => {
            const newContent = editor.querySelector('.editor-content').innerHTML;
            this.updateBlockContent(block.id, newContent);
            modal.style.display = 'none';
        });
    }

    updateBlockContent(blockId, newContent) {
        // Mettre à jour dans la configuration
        const block = this.currentPage.blocks.find(b => b.id === blockId);
        if (block) {
            block.content = newContent;
        }

        // Mettre à jour dans l'interface
        const blockElement = document.querySelector(`[data-block-id="${blockId}"] .block-content`);
        if (blockElement) {
            blockElement.innerHTML = newContent;
        }

        this.updatePreview();
    }

    updatePageType(pageType) {
        this.currentPage.type = pageType;

        // Charger un template prédéfini selon le type
        switch (pageType) {
            case 'horaires':
                this.loadScheduleTemplate();
                break;
            case 'annonces':
                this.loadAnnouncementTemplate();
                break;
            case 'evenements':
                this.loadEventTemplate();
                break;
            case 'custom':
                // Laisser l'utilisateur construire
                break;
        }
    }

    loadScheduleTemplate() {
        this.clearPageBuilder();
        this.addBlockToPage('title');
        this.addBlockToPage('schedule');
        this.addBlockToPage('contact');

        // Mettre à jour le titre
        document.getElementById('page-title').value = 'Horaires des Cours';
        this.currentPage.title = 'Horaires des Cours';
    }

    loadAnnouncementTemplate() {
        this.clearPageBuilder();
        this.addBlockToPage('title');
        this.addBlockToPage('announcement');
        this.addBlockToPage('separator');
        this.addBlockToPage('text');

        document.getElementById('page-title').value = 'Annonces du Club';
        this.currentPage.title = 'Annonces du Club';
    }

    loadEventTemplate() {
        this.clearPageBuilder();
        this.addBlockToPage('title');
        this.addBlockToPage('text');
        this.addBlockToPage('table');
        this.addBlockToPage('contact');

        document.getElementById('page-title').value = 'Événements à Venir';
        this.currentPage.title = 'Événements à Venir';
    }

    clearPageBuilder() {
        const pageBuilder = document.getElementById('page-builder');
        pageBuilder.innerHTML = '';
        this.currentPage.blocks = [];
        document.querySelector('.drop-zone-content').style.display = 'block';
    }

    updatePageColors() {
        const primary = document.getElementById('page-primary-color').value;
        const secondary = document.getElementById('page-secondary-color').value;
        const text = document.getElementById('page-text-color').value;

        this.currentPage.style.colors = {
            primary,
            secondary,
            text
        };

        this.updatePreview();
    }

    showPreview() {
        const previewPanel = document.getElementById('info-preview');
        previewPanel.style.display = 'block';
        this.generatePreview();
    }

    generatePreview() {
        const previewContent = document.getElementById('info-preview-content');
        const html = this.generatePageHTML();
        previewContent.innerHTML = html;
    }

    generatePageHTML() {
        const { title, style, blocks } = this.currentPage;

        let html = `
            <div class="info-page" style="
                --primary-color: ${style.colors.primary};
                --secondary-color: ${style.colors.secondary};
                --text-color: ${style.colors.text};
            ">
                <div class="page-header">
                    <h1>${title || 'Page d\'Information'}</h1>
                    <div class="club-logo">
                        <i class="fas fa-anchor"></i>
                        Club Nautique de Coutainville
                    </div>
                </div>
                <div class="page-content">
        `;

        // Trier les blocs par ordre et générer le HTML
        const sortedBlocks = blocks.sort((a, b) => a.order - b.order);
        sortedBlocks.forEach(block => {
            html += `<div class="content-block">${block.content}</div>`;
        });

        html += `
                </div>
                <div class="page-footer">
                    <div class="update-info">
                        Dernière mise à jour: ${new Date().toLocaleString()}
                    </div>
                </div>
            </div>
        `;

        return html;
    }

    updatePreview() {
        const previewPanel = document.getElementById('info-preview');
        if (previewPanel.style.display === 'block') {
            this.generatePreview();
        }
    }

    saveInfoPage() {
        const name = this.currentPage.title || prompt('Nom de la page d\'information:');
        if (!name) return;

        const pageConfig = {
            name,
            type: 'info-page',
            config: this.currentPage,
            created: new Date().toISOString()
        };

        // Sauvegarder dans localStorage
        const savedPages = JSON.parse(localStorage.getItem('infoPages') || '{}');
        savedPages[name] = pageConfig;
        localStorage.setItem('infoPages', JSON.stringify(savedPages));

        // Ajouter à la playlist comme nouveau type de contenu
        if (window.playlistManagerImproved) {
            window.playlistManagerImproved.addCustomContent(pageConfig);
        }

        alert(`Page "${name}" sauvegardée !`);
    }

    loadDefaultConfig() {
        // Charger une configuration par défaut
        this.updatePageType('horaires');
    }

    closeEditor() {
        // Chercher tous les conteneurs d'éditeur de pages possibles
        const containers = document.querySelectorAll('.info-editor-container');
        containers.forEach(container => {
            if (container && container.parentNode) {
                container.parentNode.removeChild(container);
                console.log('Éditeur de pages fermé');
            }
        });

        // Nettoyer les références
        if (window.nautiflixEditors && window.nautiflixEditors.infoEditor === this) {
            window.nautiflixEditors.infoEditor = null;
        }
    }
}

// Export pour utilisation
window.InfoPageEditor = InfoPageEditor;
