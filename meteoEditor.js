// Éditeur Météo Intégré pour Nautiflix
// Permet de créer des écrans météo personnalisés avec les données Windguru/Arome

class MeteoEditor {
    constructor() {
        this.currentConfig = {
            layout: 'semaine', // 'semaine', 'today', 'vent6h'
            dataSource: 'windguru', // 'windguru', 'arome'
            widgets: [],
            style: {
                theme: 'nautique',
                colors: {
                    primary: '#2c5aa0',
                    secondary: '#87ceeb',
                    accent: '#ff6b35'
                }
            }
        };
        this.availableWidgets = this.initializeWidgets();
    }

    initializeWidgets() {
        return {
            temperature: {
                name: 'Température',
                icon: 'fas fa-thermometer-half',
                type: 'gauge',
                dataKey: 'temperature',
                unit: '°C'
            },
            wind: {
                name: 'Vent',
                icon: 'fas fa-wind',
                type: 'compass',
                dataKey: 'wind',
                unit: 'km/h'
            },
            pressure: {
                name: 'Pression',
                icon: 'fas fa-tachometer-alt',
                type: 'gauge',
                dataKey: 'pressure',
                unit: 'hPa'
            },
            humidity: {
                name: '<PERSON><PERSON><PERSON><PERSON>',
                icon: 'fas fa-tint',
                type: 'percentage',
                dataKey: 'humidity',
                unit: '%'
            },
            waves: {
                name: 'Vagues',
                icon: 'fas fa-water',
                type: 'wave',
                dataKey: 'waves',
                unit: 'm'
            },
            forecast: {
                name: 'Prévisions 6H',
                icon: 'fas fa-clock',
                type: 'timeline',
                dataKey: 'forecast6h',
                unit: ''
            },
            weekForecast: {
                name: 'Semaine',
                icon: 'fas fa-calendar-week',
                type: 'weekly',
                dataKey: 'weekForecast',
                unit: ''
            },
            tides: {
                name: 'Marées',
                icon: 'fas fa-arrows-alt-v',
                type: 'tides',
                dataKey: 'tides',
                unit: 'm'
            }
        };
    }

    createEditor() {
        return `
            <div class="meteo-editor-container">
                <div class="editor-header">
                    <h2><i class="fas fa-cloud-sun"></i> Éditeur Météo Personnalisé</h2>
                    <div class="header-controls">
                        <button id="preview-meteo" class="btn-preview">
                            <i class="fas fa-eye"></i> Aperçu
                        </button>
                        <button id="save-meteo-config" class="btn-save">
                            <i class="fas fa-save"></i> Sauvegarder
                        </button>
                        <button id="close-meteo-editor" class="btn-close">&times;</button>
                    </div>
                </div>

                <div class="editor-content">
                    <!-- Panneau de configuration -->
                    <div class="config-panel">
                        <div class="config-section">
                            <h3><i class="fas fa-cog"></i> Configuration</h3>
                            
                            <div class="config-group">
                                <label>Type d'affichage :</label>
                                <select id="layout-type" class="config-select">
                                    <option value="semaine">Vue Semaine</option>
                                    <option value="today">Aujourd'hui</option>
                                    <option value="vent6h">Vent 6H</option>
                                    <option value="custom">Personnalisé</option>
                                </select>
                            </div>

                            <div class="config-group">
                                <label>Source de données :</label>
                                <select id="data-source" class="config-select">
                                    <option value="windguru">Windguru</option>
                                    <option value="arome">Arome (Météo France)</option>
                                    <option value="combined">Combiné</option>
                                </select>
                            </div>

                            <div class="config-group">
                                <label>Thème :</label>
                                <select id="theme-select" class="config-select">
                                    <option value="nautique">Nautique</option>
                                    <option value="moderne">Moderne</option>
                                    <option value="classique">Classique</option>
                                </select>
                            </div>
                        </div>

                        <div class="config-section">
                            <h3><i class="fas fa-palette"></i> Couleurs</h3>
                            <div class="color-picker-group">
                                <div class="color-input">
                                    <label>Couleur principale :</label>
                                    <input type="color" id="primary-color" value="#2c5aa0">
                                </div>
                                <div class="color-input">
                                    <label>Couleur secondaire :</label>
                                    <input type="color" id="secondary-color" value="#87ceeb">
                                </div>
                                <div class="color-input">
                                    <label>Couleur accent :</label>
                                    <input type="color" id="accent-color" value="#ff6b35">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Panneau des widgets disponibles -->
                    <div class="widgets-panel">
                        <h3><i class="fas fa-puzzle-piece"></i> Widgets Disponibles</h3>
                        <div class="widgets-grid" id="available-widgets">
                            <!-- Généré dynamiquement -->
                        </div>
                    </div>

                    <!-- Zone de construction -->
                    <div class="builder-panel">
                        <h3><i class="fas fa-th-large"></i> Zone de Construction</h3>
                        <div class="drop-zone" id="meteo-drop-zone">
                            <div class="drop-zone-content">
                                <i class="fas fa-plus-circle"></i>
                                <p>Glissez les widgets ici pour construire votre écran météo</p>
                            </div>
                            <div class="builder-grid" id="builder-grid">
                                <!-- Widgets ajoutés apparaîtront ici -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Panneau de prévisualisation -->
                <div class="preview-panel" id="meteo-preview" style="display: none;">
                    <div class="preview-header">
                        <h3><i class="fas fa-eye"></i> Aperçu en Temps Réel</h3>
                        <button id="close-preview" class="btn-close">&times;</button>
                    </div>
                    <div class="preview-content" id="preview-content">
                        <!-- Aperçu généré ici -->
                    </div>
                </div>
            </div>
        `;
    }

    initializeEditor() {
        // Générer les widgets disponibles
        this.generateAvailableWidgets();
        
        // Initialiser les événements
        this.setupEventListeners();
        
        // Charger la configuration par défaut
        this.loadDefaultConfig();
    }

    generateAvailableWidgets() {
        const container = document.getElementById('available-widgets');
        container.innerHTML = '';

        Object.entries(this.availableWidgets).forEach(([key, widget]) => {
            const widgetElement = document.createElement('div');
            widgetElement.className = 'widget-item';
            widgetElement.draggable = true;
            widgetElement.dataset.widgetType = key;
            
            widgetElement.innerHTML = `
                <div class="widget-icon">
                    <i class="${widget.icon}"></i>
                </div>
                <div class="widget-info">
                    <span class="widget-name">${widget.name}</span>
                    <span class="widget-type">${widget.type}</span>
                </div>
            `;

            // Événements drag & drop
            widgetElement.addEventListener('dragstart', (e) => {
                e.dataTransfer.setData('text/plain', key);
            });

            container.appendChild(widgetElement);
        });
    }

    setupEventListeners() {
        // Gestion du drop zone
        const dropZone = document.getElementById('meteo-drop-zone');
        const builderGrid = document.getElementById('builder-grid');

        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('drag-over');
        });

        dropZone.addEventListener('dragleave', () => {
            dropZone.classList.remove('drag-over');
        });

        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('drag-over');
            
            const widgetType = e.dataTransfer.getData('text/plain');
            this.addWidgetToBuilder(widgetType);
        });

        // Boutons de contrôle
        document.getElementById('preview-meteo').addEventListener('click', () => {
            this.showPreview();
        });

        document.getElementById('save-meteo-config').addEventListener('click', () => {
            this.saveConfiguration();
        });

        document.getElementById('close-meteo-editor').addEventListener('click', () => {
            this.closeEditor();
        });

        // Changements de configuration
        document.getElementById('layout-type').addEventListener('change', (e) => {
            this.updateLayout(e.target.value);
        });

        document.getElementById('data-source').addEventListener('change', (e) => {
            this.updateDataSource(e.target.value);
        });

        // Changements de couleurs
        ['primary-color', 'secondary-color', 'accent-color'].forEach(id => {
            document.getElementById(id).addEventListener('change', (e) => {
                this.updateColors();
            });
        });
    }

    addWidgetToBuilder(widgetType) {
        const widget = this.availableWidgets[widgetType];
        if (!widget) return;

        const widgetId = `widget-${Date.now()}`;
        const builderGrid = document.getElementById('builder-grid');

        // Masquer le message de drop zone si c'est le premier widget
        const dropZoneContent = document.querySelector('.drop-zone-content');
        if (dropZoneContent) {
            dropZoneContent.style.display = 'none';
        }

        const widgetElement = document.createElement('div');
        widgetElement.className = 'builder-widget';
        widgetElement.dataset.widgetId = widgetId;
        widgetElement.dataset.widgetType = widgetType;

        widgetElement.innerHTML = `
            <div class="widget-header">
                <span class="widget-title">
                    <i class="${widget.icon}"></i>
                    ${widget.name}
                </span>
                <div class="widget-controls">
                    <button class="widget-config" title="Configurer">
                        <i class="fas fa-cog"></i>
                    </button>
                    <button class="widget-remove" title="Supprimer">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="widget-preview">
                ${this.generateWidgetPreview(widget)}
            </div>
        `;

        // Événements pour ce widget
        widgetElement.querySelector('.widget-remove').addEventListener('click', () => {
            this.removeWidget(widgetId);
        });

        widgetElement.querySelector('.widget-config').addEventListener('click', () => {
            this.configureWidget(widgetId, widgetType);
        });

        builderGrid.appendChild(widgetElement);

        // Ajouter à la configuration
        this.currentConfig.widgets.push({
            id: widgetId,
            type: widgetType,
            config: { ...widget }
        });

        this.updatePreview();
    }

    generateWidgetPreview(widget) {
        switch (widget.type) {
            case 'gauge':
                return `<div class="preview-gauge">
                    <div class="gauge-value">23${widget.unit}</div>
                    <div class="gauge-label">${widget.name}</div>
                </div>`;
            
            case 'compass':
                return `<div class="preview-compass">
                    <div class="compass-direction">NE</div>
                    <div class="compass-speed">15 ${widget.unit}</div>
                </div>`;
            
            case 'timeline':
                return `<div class="preview-timeline">
                    <div class="timeline-hours">6H | 12H | 18H</div>
                    <div class="timeline-values">15° | 18° | 16°</div>
                </div>`;
            
            case 'weekly':
                return `<div class="preview-weekly">
                    <div class="week-days">L M M J V S D</div>
                    <div class="week-temps">15 17 19 16 14 18 20</div>
                </div>`;
            
            default:
                return `<div class="preview-default">
                    <i class="${widget.icon}"></i>
                    <span>${widget.name}</span>
                </div>`;
        }
    }

    removeWidget(widgetId) {
        // Supprimer de l'interface
        const widgetElement = document.querySelector(`[data-widget-id="${widgetId}"]`);
        if (widgetElement) {
            widgetElement.remove();
        }

        // Supprimer de la configuration
        this.currentConfig.widgets = this.currentConfig.widgets.filter(w => w.id !== widgetId);

        // Réafficher le message si plus de widgets
        const builderGrid = document.getElementById('builder-grid');
        if (builderGrid.children.length === 0) {
            document.querySelector('.drop-zone-content').style.display = 'block';
        }

        this.updatePreview();
    }

    configureWidget(widgetId, widgetType) {
        // Ouvrir un panneau de configuration spécifique au widget
        const widget = this.currentConfig.widgets.find(w => w.id === widgetId);
        if (!widget) return;

        const configPanel = this.createWidgetConfigPanel(widget);
        document.body.appendChild(configPanel);
    }

    createWidgetConfigPanel(widget) {
        const panel = document.createElement('div');
        panel.className = 'widget-config-panel';
        panel.innerHTML = `
            <div class="config-panel-content">
                <div class="config-panel-header">
                    <h3>Configuration - ${widget.config.name}</h3>
                    <button class="close-config-panel">&times;</button>
                </div>
                <div class="config-panel-body">
                    <div class="config-option">
                        <label>Taille :</label>
                        <select class="widget-size">
                            <option value="small">Petit</option>
                            <option value="medium" selected>Moyen</option>
                            <option value="large">Grand</option>
                        </select>
                    </div>
                    <div class="config-option">
                        <label>Position :</label>
                        <select class="widget-position">
                            <option value="auto" selected>Automatique</option>
                            <option value="top-left">Haut Gauche</option>
                            <option value="top-right">Haut Droite</option>
                            <option value="bottom-left">Bas Gauche</option>
                            <option value="bottom-right">Bas Droite</option>
                        </select>
                    </div>
                    <div class="config-option">
                        <label>Affichage des unités :</label>
                        <input type="checkbox" class="show-units" checked>
                    </div>
                </div>
                <div class="config-panel-footer">
                    <button class="btn-cancel">Annuler</button>
                    <button class="btn-apply">Appliquer</button>
                </div>
            </div>
        `;

        // Événements
        panel.querySelector('.close-config-panel').addEventListener('click', () => {
            panel.remove();
        });

        panel.querySelector('.btn-cancel').addEventListener('click', () => {
            panel.remove();
        });

        panel.querySelector('.btn-apply').addEventListener('click', () => {
            this.applyWidgetConfig(widget, panel);
            panel.remove();
        });

        return panel;
    }

    applyWidgetConfig(widget, panel) {
        const size = panel.querySelector('.widget-size').value;
        const position = panel.querySelector('.widget-position').value;
        const showUnits = panel.querySelector('.show-units').checked;

        widget.config.size = size;
        widget.config.position = position;
        widget.config.showUnits = showUnits;

        this.updatePreview();
    }

    updateLayout(layoutType) {
        this.currentConfig.layout = layoutType;

        // Charger un template prédéfini selon le type
        switch (layoutType) {
            case 'semaine':
                this.loadWeekTemplate();
                break;
            case 'today':
                this.loadTodayTemplate();
                break;
            case 'vent6h':
                this.loadWind6hTemplate();
                break;
            case 'custom':
                // Laisser l'utilisateur construire
                break;
        }
    }

    loadWeekTemplate() {
        // Charger le template basé sur meteoSemaine.html
        this.clearBuilder();
        this.addWidgetToBuilder('weekForecast');
        this.addWidgetToBuilder('wind');
        this.addWidgetToBuilder('temperature');
        this.addWidgetToBuilder('tides');
    }

    loadTodayTemplate() {
        // Charger le template basé sur meteotoday.html
        this.clearBuilder();
        this.addWidgetToBuilder('temperature');
        this.addWidgetToBuilder('wind');
        this.addWidgetToBuilder('pressure');
        this.addWidgetToBuilder('humidity');
        this.addWidgetToBuilder('waves');
    }

    loadWind6hTemplate() {
        // Charger le template basé sur vent6H.html
        this.clearBuilder();
        this.addWidgetToBuilder('forecast');
        this.addWidgetToBuilder('wind');
    }

    clearBuilder() {
        const builderGrid = document.getElementById('builder-grid');
        builderGrid.innerHTML = '';
        this.currentConfig.widgets = [];
        document.querySelector('.drop-zone-content').style.display = 'block';
    }

    updateDataSource(source) {
        this.currentConfig.dataSource = source;
        this.updatePreview();
    }

    updateColors() {
        const primary = document.getElementById('primary-color').value;
        const secondary = document.getElementById('secondary-color').value;
        const accent = document.getElementById('accent-color').value;

        this.currentConfig.style.colors = {
            primary,
            secondary,
            accent
        };

        this.updatePreview();
    }

    showPreview() {
        const previewPanel = document.getElementById('meteo-preview');
        previewPanel.style.display = 'block';
        this.generatePreview();
    }

    generatePreview() {
        const previewContent = document.getElementById('preview-content');
        const html = this.generateMeteoHTML();
        previewContent.innerHTML = html;
    }

    generateMeteoHTML() {
        const { layout, style, widgets } = this.currentConfig;

        let html = `
            <div class="meteo-display" style="
                --primary-color: ${style.colors.primary};
                --secondary-color: ${style.colors.secondary};
                --accent-color: ${style.colors.accent};
            ">
                <div class="meteo-header">
                    <h2><i class="fas fa-cloud-sun"></i> Conditions Météo - Club Nautique de Coutainville</h2>
                    <div class="update-time">Dernière mise à jour: ${new Date().toLocaleTimeString()}</div>
                </div>
                <div class="meteo-grid layout-${layout}">
        `;

        widgets.forEach(widget => {
            html += this.generateWidgetHTML(widget);
        });

        html += `
                </div>
            </div>
        `;

        return html;
    }

    generateWidgetHTML(widget) {
        const { type, config } = widget;
        const size = config.size || 'medium';
        const position = config.position || 'auto';

        switch (type) {
            case 'temperature':
                return `
                    <div class="widget widget-temperature size-${size} pos-${position}">
                        <div class="widget-header">
                            <i class="fas fa-thermometer-half"></i>
                            <span>Température</span>
                        </div>
                        <div class="widget-content">
                            <div class="temp-current">18°C</div>
                            <div class="temp-feels">Ressenti: 16°C</div>
                        </div>
                    </div>
                `;

            case 'wind':
                return `
                    <div class="widget widget-wind size-${size} pos-${position}">
                        <div class="widget-header">
                            <i class="fas fa-wind"></i>
                            <span>Vent</span>
                        </div>
                        <div class="widget-content">
                            <div class="wind-speed">15 km/h</div>
                            <div class="wind-direction">Nord-Est</div>
                            <div class="wind-compass">
                                <div class="compass-arrow" style="transform: rotate(45deg)"></div>
                            </div>
                        </div>
                    </div>
                `;

            case 'weekForecast':
                return `
                    <div class="widget widget-week size-${size} pos-${position}">
                        <div class="widget-header">
                            <i class="fas fa-calendar-week"></i>
                            <span>Prévisions 7 jours</span>
                        </div>
                        <div class="widget-content">
                            <div class="week-forecast">
                                ${this.generateWeekForecast()}
                            </div>
                        </div>
                    </div>
                `;

            default:
                return `
                    <div class="widget widget-${type} size-${size} pos-${position}">
                        <div class="widget-header">
                            <i class="${config.icon}"></i>
                            <span>${config.name}</span>
                        </div>
                        <div class="widget-content">
                            <div class="widget-value">--</div>
                        </div>
                    </div>
                `;
        }
    }

    generateWeekForecast() {
        const days = ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'];
        const temps = [16, 18, 19, 17, 15, 20, 22];
        const winds = [12, 15, 8, 20, 25, 10, 5];

        return days.map((day, index) => `
            <div class="day-forecast">
                <div class="day-name">${day}</div>
                <div class="day-temp">${temps[index]}°</div>
                <div class="day-wind">${winds[index]}km/h</div>
            </div>
        `).join('');
    }

    updatePreview() {
        const previewPanel = document.getElementById('meteo-preview');
        if (previewPanel.style.display === 'block') {
            this.generatePreview();
        }
    }

    saveConfiguration() {
        const name = prompt('Nom de la configuration météo:');
        if (!name) return;

        const config = {
            name,
            type: 'meteo-custom',
            config: this.currentConfig,
            created: new Date().toISOString()
        };

        // Sauvegarder dans localStorage
        const savedConfigs = JSON.parse(localStorage.getItem('meteoConfigs') || '{}');
        savedConfigs[name] = config;
        localStorage.setItem('meteoConfigs', JSON.stringify(savedConfigs));

        // Ajouter à la playlist comme nouveau type de contenu
        if (window.playlistManagerImproved) {
            window.playlistManagerImproved.addCustomContent(config);
        }

        alert(`Configuration "${name}" sauvegardée !`);
    }

    loadDefaultConfig() {
        // Charger une configuration par défaut basée sur meteotoday
        this.updateLayout('today');
    }

    closeEditor() {
        // Chercher tous les conteneurs d'éditeur météo possibles
        const containers = document.querySelectorAll('.meteo-editor-container');
        containers.forEach(container => {
            if (container && container.parentNode) {
                container.parentNode.removeChild(container);
                console.log('Éditeur météo fermé');
            }
        });

        // Nettoyer les références
        if (window.nautiflixEditors && window.nautiflixEditors.meteoEditor === this) {
            window.nautiflixEditors.meteoEditor = null;
        }
    }
}

// Export pour utilisation
window.MeteoEditor = MeteoEditor;
