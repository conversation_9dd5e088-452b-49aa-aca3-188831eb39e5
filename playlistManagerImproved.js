// Gestionnaire de playlist amélioré pour Nautiflix
let currentPlaylist = [];
let savedPlaylists = JSON.parse(localStorage.getItem('savedPlaylists') || '{}');
let draggedItem = null;

function initializeImprovedPlaylistManager() {
    const playlistPopup = document.getElementById('playlist-popup');
    
    // Structure HTML améliorée
    playlistPopup.innerHTML = `
        <div class="playlist-header-improved">
            <div class="header-content">
                <h2><i class="fas fa-list-ul"></i> Gestionnaire de Playlist</h2>
                <div class="header-controls">
                    <button id="audio-toggle" class="audio-btn" title="Activer/Désactiver l'audio">
                        <i class="fas fa-volume-up"></i>
                    </button>
                    <button id="close-playlist" class="close-btn">&times;</button>
                </div>
            </div>
        </div>

        <div class="playlist-content-improved">
            <!-- Section de contrôles -->
            <div class="playlist-toolbar">
                <div class="toolbar-section">
                    <button id="add-item-improved" class="btn-primary">
                        <i class="fas fa-plus"></i> Ajouter un média
                    </button>
                    <button id="clear-playlist" class="btn-secondary">
                        <i class="fas fa-trash"></i> Vider la playlist
                    </button>
                </div>
                
                <div class="toolbar-section">
                    <div class="playlist-save-improved">
                        <input type="text" id="playlist-name-improved" placeholder="Nom de la playlist" class="input-field">
                        <button id="save-playlist-improved" class="btn-success">
                            <i class="fas fa-save"></i> Sauvegarder
                        </button>
                    </div>
                </div>
                
                <div class="toolbar-section">
                    <div class="playlist-load-improved">
                        <select id="playlist-select-improved" class="select-field"></select>
                        <button id="load-playlist-improved" class="btn-info">
                            <i class="fas fa-folder-open"></i> Charger
                        </button>
                        <button id="delete-playlist-improved" class="btn-danger">
                            <i class="fas fa-trash-alt"></i> Supprimer
                        </button>
                    </div>
                </div>
            </div>

            <!-- Zone de drop pour les fichiers -->
            <div id="drop-zone" class="drop-zone">
                <div class="drop-zone-content">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <p>Glissez-déposez vos fichiers ici ou cliquez pour sélectionner</p>
                    <input type="file" id="file-input-improved" multiple accept="video/*,image/*">
                </div>
            </div>

            <!-- Liste des éléments de la playlist -->
            <div class="playlist-items-container">
                <div class="playlist-header-info">
                    <span id="playlist-count">0 éléments</span>
                    <span id="playlist-duration">Durée totale: 0:00</span>
                </div>
                <ul id="playlist-items-improved" class="playlist-items-list"></ul>
            </div>

            <!-- Panel d'ajout de média amélioré -->
            <div id="add-media-panel-improved" class="add-media-panel" style="display: none;">
                <div class="panel-header">
                    <h3><i class="fas fa-plus-circle"></i> Ajouter un média</h3>
                    <button id="add-media-close" class="close-btn">&times;</button>
                </div>
                
                <div class="panel-content">
                    <div class="media-type-selector">
                        <label>Type de média :</label>
                        <div class="radio-group">
                            <label class="radio-option">
                                <input type="radio" name="media-type" value="image" checked>
                                <i class="fas fa-image"></i> Image
                            </label>
                            <label class="radio-option">
                                <input type="radio" name="media-type" value="video">
                                <i class="fas fa-video"></i> Vidéo
                            </label>
                            <label class="radio-option">
                                <input type="radio" name="media-type" value="webpage">
                                <i class="fas fa-globe"></i> Page Web
                            </label>
                        </div>
                    </div>
                    
                    <div class="input-group">
                        <label for="file-input-panel">Fichier :</label>
                        <input type="file" id="file-input-panel" class="input-field">
                    </div>
                    
                    <div class="input-group">
                        <label for="url-input-improved">URL :</label>
                        <input type="text" id="url-input-improved" placeholder="https://..." class="input-field">
                    </div>
                    
                    <div class="input-group">
                        <label for="duration-input-improved">Durée (secondes) :</label>
                        <input type="number" id="duration-input-improved" value="10" min="1" class="input-field">
                    </div>
                    
                    <div class="panel-buttons">
                        <button id="add-media-cancel-improved" class="btn-secondary">
                            <i class="fas fa-times"></i> Annuler
                        </button>
                        <button id="add-media-confirm-improved" class="btn-primary">
                            <i class="fas fa-check"></i> Ajouter
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Ajouter les styles CSS améliorés
    addImprovedStyles();
    
    // Initialiser les événements
    initializeImprovedEvents();
    
    // Charger les playlists sauvegardées
    updatePlaylistSelectImproved();
    
    // Mettre à jour l'affichage
    updatePlaylistDisplayImproved();
}

function addImprovedStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .playlist-header-improved {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px 12px 0 0;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header-controls {
            display: flex;
            gap: 10px;
        }
        
        .audio-btn, .close-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .audio-btn:hover, .close-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.05);
        }
        
        .playlist-content-improved {
            padding: 20px;
            max-height: 70vh;
            overflow-y: auto;
        }
        
        .playlist-toolbar {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .toolbar-section {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .btn-primary, .btn-secondary, .btn-success, .btn-info, .btn-danger {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        
        .btn-primary:hover { background: #0056b3; }
        .btn-secondary:hover { background: #545b62; }
        .btn-success:hover { background: #1e7e34; }
        .btn-info:hover { background: #117a8b; }
        .btn-danger:hover { background: #bd2130; }
        
        .drop-zone {
            border: 2px dashed #007bff;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .drop-zone:hover, .drop-zone.dragover {
            border-color: #0056b3;
            background: rgba(0,123,255,0.1);
        }
        
        .drop-zone-content i {
            font-size: 48px;
            color: #007bff;
            margin-bottom: 10px;
        }
        
        .playlist-items-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .playlist-header-info {
            display: flex;
            justify-content: space-between;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px 8px 0 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .playlist-items-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .input-field, .select-field {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .add-media-panel {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            z-index: 1000;
            min-width: 500px;
        }
        
        .panel-header {
            background: #007bff;
            color: white;
            padding: 15px 20px;
            border-radius: 12px 12px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .panel-content {
            padding: 20px;
        }
        
        .radio-group {
            display: flex;
            gap: 15px;
            margin-top: 5px;
        }
        
        .radio-option {
            display: flex;
            align-items: center;
            gap: 5px;
            cursor: pointer;
        }
        
        .input-group {
            margin-bottom: 15px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .panel-buttons {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
        }
    `;
    document.head.appendChild(style);
}

function initializeImprovedEvents() {
    // Événements pour les boutons principaux
    document.getElementById('add-item-improved').addEventListener('click', showAddMediaPanel);
    document.getElementById('clear-playlist').addEventListener('click', clearPlaylist);
    document.getElementById('close-playlist').addEventListener('click', closePlaylistManager);

    // Événements pour la sauvegarde/chargement
    document.getElementById('save-playlist-improved').addEventListener('click', savePlaylistImproved);
    document.getElementById('load-playlist-improved').addEventListener('click', loadPlaylistImproved);
    document.getElementById('delete-playlist-improved').addEventListener('click', deletePlaylistImproved);

    // Événements pour le panel d'ajout
    document.getElementById('add-media-close').addEventListener('click', hideAddMediaPanel);
    document.getElementById('add-media-cancel-improved').addEventListener('click', hideAddMediaPanel);
    document.getElementById('add-media-confirm-improved').addEventListener('click', addMediaImproved);

    // Événements pour le drag & drop
    const dropZone = document.getElementById('drop-zone');
    const fileInput = document.getElementById('file-input-improved');

    dropZone.addEventListener('click', () => fileInput.click());
    fileInput.addEventListener('change', handleFileSelection);

    // Drag & drop events
    dropZone.addEventListener('dragover', handleDragOver);
    dropZone.addEventListener('dragleave', handleDragLeave);
    dropZone.addEventListener('drop', handleFileDrop);

    // Contrôle audio global
    document.getElementById('audio-toggle').addEventListener('click', toggleGlobalAudio);
}

function showAddMediaPanel() {
    document.getElementById('add-media-panel-improved').style.display = 'block';
}

function hideAddMediaPanel() {
    document.getElementById('add-media-panel-improved').style.display = 'none';
    resetAddMediaForm();
}

function resetAddMediaForm() {
    document.getElementById('file-input-panel').value = '';
    document.getElementById('url-input-improved').value = '';
    document.getElementById('duration-input-improved').value = '10';
    document.querySelector('input[name="media-type"][value="image"]').checked = true;
}

function handleFileSelection(event) {
    const files = event.target.files;
    processFiles(files);
}

function handleDragOver(event) {
    event.preventDefault();
    event.currentTarget.classList.add('dragover');
}

function handleDragLeave(event) {
    event.currentTarget.classList.remove('dragover');
}

function handleFileDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('dragover');
    const files = event.dataTransfer.files;
    processFiles(files);
}

function processFiles(files) {
    Array.from(files).forEach(file => {
        const fileType = getFileType(file);
        if (fileType) {
            const reader = new FileReader();
            reader.onloadend = function() {
                addItemToPlaylistImproved({
                    type: fileType,
                    content: reader.result,
                    duration: fileType === 'video' ? 0 : 10000, // Durée par défaut pour images
                    name: file.name,
                    size: file.size
                });
            };
            reader.readAsDataURL(file);
        } else {
            showNotificationImproved('Type de fichier non supporté: ' + file.name, 'error');
        }
    });
}

function getFileType(file) {
    if (file.type.startsWith('image/')) return 'image';
    if (file.type.startsWith('video/')) return 'video';
    return null;
}

function addMediaImproved() {
    const mediaType = document.querySelector('input[name="media-type"]:checked').value;
    const fileInput = document.getElementById('file-input-panel');
    const urlInput = document.getElementById('url-input-improved');
    const durationInput = document.getElementById('duration-input-improved');

    if (fileInput.files.length > 0) {
        const file = fileInput.files[0];
        const reader = new FileReader();
        reader.onloadend = function() {
            addItemToPlaylistImproved({
                type: mediaType,
                content: reader.result,
                duration: parseInt(durationInput.value) * 1000,
                name: file.name,
                size: file.size
            });
        };
        reader.readAsDataURL(file);
    } else if (urlInput.value) {
        addItemToPlaylistImproved({
            type: mediaType,
            content: urlInput.value,
            duration: parseInt(durationInput.value) * 1000,
            name: urlInput.value.split('/').pop() || 'URL Media',
            size: 0
        });
    } else {
        showNotificationImproved('Veuillez sélectionner un fichier ou entrer une URL', 'error');
        return;
    }

    hideAddMediaPanel();
}

function addItemToPlaylistImproved(item) {
    // Ajouter un ID unique
    item.id = Date.now() + Math.random();
    currentPlaylist.push(item);
    updatePlaylistDisplayImproved();
    showNotificationImproved('Média ajouté à la playlist', 'success');
}

function updatePlaylistDisplayImproved() {
    const container = document.getElementById('playlist-items-improved');
    const countElement = document.getElementById('playlist-count');
    const durationElement = document.getElementById('playlist-duration');

    container.innerHTML = '';

    // Mettre à jour les statistiques
    countElement.textContent = `${currentPlaylist.length} élément${currentPlaylist.length > 1 ? 's' : ''}`;
    const totalDuration = currentPlaylist.reduce((sum, item) => sum + (item.duration || 0), 0);
    durationElement.textContent = `Durée totale: ${formatDuration(totalDuration / 1000)}`;

    currentPlaylist.forEach((item, index) => {
        const li = document.createElement('li');
        li.className = 'playlist-item-improved';
        li.draggable = true;
        li.dataset.index = index;

        const thumbnail = createThumbnail(item);
        const typeIcon = getTypeIcon(item.type);
        const sizeText = item.size ? formatFileSize(item.size) : '';

        li.innerHTML = `
            <div class="item-thumbnail">${thumbnail}</div>
            <div class="item-info">
                <div class="item-title">
                    <i class="${typeIcon}"></i>
                    <span>${item.name}</span>
                </div>
                <div class="item-details">
                    <span class="item-duration">${formatDuration((item.duration || 0) / 1000)}</span>
                    ${sizeText ? `<span class="item-size">${sizeText}</span>` : ''}
                </div>
            </div>
            <div class="item-controls">
                <button onclick="moveItemUp(${index})" class="btn-move" title="Monter">
                    <i class="fas fa-arrow-up"></i>
                </button>
                <button onclick="moveItemDown(${index})" class="btn-move" title="Descendre">
                    <i class="fas fa-arrow-down"></i>
                </button>
                <button onclick="deleteItemImproved(${index})" class="btn-delete" title="Supprimer">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;

        // Ajouter les événements de drag & drop
        li.addEventListener('dragstart', handleDragStart);
        li.addEventListener('dragover', handleDragOverItem);
        li.addEventListener('drop', handleDropItem);

        container.appendChild(li);
    });

    // Ajouter les styles pour les nouveaux éléments
    addPlaylistItemStyles();
}

function addPlaylistItemStyles() {
    if (document.getElementById('playlist-item-styles')) return;

    const style = document.createElement('style');
    style.id = 'playlist-item-styles';
    style.textContent = `
        .playlist-item-improved {
            display: flex;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
            transition: all 0.3s ease;
            cursor: move;
        }

        .playlist-item-improved:hover {
            background: #f8f9fa;
        }

        .playlist-item-improved:last-child {
            border-bottom: none;
        }

        .item-thumbnail {
            width: 60px;
            height: 60px;
            margin-right: 15px;
            border-radius: 6px;
            overflow: hidden;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .item-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .item-thumbnail i {
            font-size: 24px;
            color: #6c757d;
        }

        .item-info {
            flex: 1;
        }

        .item-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            margin-bottom: 5px;
        }

        .item-details {
            display: flex;
            gap: 15px;
            font-size: 12px;
            color: #6c757d;
        }

        .item-controls {
            display: flex;
            gap: 5px;
        }

        .btn-move, .btn-delete {
            padding: 6px 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .btn-move {
            background: #007bff;
            color: white;
        }

        .btn-move:hover {
            background: #0056b3;
        }

        .btn-delete {
            background: #dc3545;
            color: white;
        }

        .btn-delete:hover {
            background: #bd2130;
        }

        .playlist-item-improved.dragging {
            opacity: 0.5;
        }
    `;
    document.head.appendChild(style);
}

function createThumbnail(item) {
    if (item.type === 'image') {
        return `<img src="${item.content}" alt="Thumbnail">`;
    } else if (item.type === 'video') {
        return `<i class="fas fa-play-circle"></i>`;
    } else if (item.type === 'webpage') {
        return `<i class="fas fa-globe"></i>`;
    }
    return `<i class="fas fa-file"></i>`;
}

function getTypeIcon(type) {
    switch (type) {
        case 'image': return 'fas fa-image';
        case 'video': return 'fas fa-video';
        case 'webpage': return 'fas fa-globe';
        default: return 'fas fa-file';
    }
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatDuration(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

// Fonctions de gestion des éléments
function moveItemUp(index) {
    if (index > 0) {
        [currentPlaylist[index], currentPlaylist[index - 1]] = [currentPlaylist[index - 1], currentPlaylist[index]];
        updatePlaylistDisplayImproved();
    }
}

function moveItemDown(index) {
    if (index < currentPlaylist.length - 1) {
        [currentPlaylist[index], currentPlaylist[index + 1]] = [currentPlaylist[index + 1], currentPlaylist[index]];
        updatePlaylistDisplayImproved();
    }
}

function deleteItemImproved(index) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cet élément ?')) {
        currentPlaylist.splice(index, 1);
        updatePlaylistDisplayImproved();
        showNotificationImproved('Élément supprimé', 'info');
    }
}

function clearPlaylist() {
    if (confirm('Êtes-vous sûr de vouloir vider la playlist ?')) {
        currentPlaylist = [];
        updatePlaylistDisplayImproved();
        showNotificationImproved('Playlist vidée', 'info');
    }
}

// Fonctions de drag & drop
function handleDragStart(event) {
    draggedItem = parseInt(event.target.dataset.index);
    event.target.classList.add('dragging');
}

function handleDragOverItem(event) {
    event.preventDefault();
}

function handleDropItem(event) {
    event.preventDefault();
    const targetIndex = parseInt(event.target.closest('.playlist-item-improved').dataset.index);

    if (draggedItem !== null && draggedItem !== targetIndex) {
        const draggedElement = currentPlaylist[draggedItem];
        currentPlaylist.splice(draggedItem, 1);
        currentPlaylist.splice(targetIndex, 0, draggedElement);
        updatePlaylistDisplayImproved();
    }

    // Nettoyer
    document.querySelectorAll('.playlist-item-improved').forEach(item => {
        item.classList.remove('dragging');
    });
    draggedItem = null;
}

// Fonctions de sauvegarde et chargement
function savePlaylistImproved() {
    const name = document.getElementById('playlist-name-improved').value.trim();
    if (!name) {
        showNotificationImproved('Veuillez entrer un nom pour la playlist', 'error');
        return;
    }

    if (currentPlaylist.length === 0) {
        showNotificationImproved('La playlist est vide', 'error');
        return;
    }

    savedPlaylists[name] = [...currentPlaylist];
    localStorage.setItem('savedPlaylists', JSON.stringify(savedPlaylists));
    updatePlaylistSelectImproved();
    showNotificationImproved(`Playlist "${name}" sauvegardée avec succès`, 'success');
    document.getElementById('playlist-name-improved').value = '';
}

function loadPlaylistImproved() {
    const name = document.getElementById('playlist-select-improved').value;
    if (!name) {
        showNotificationImproved('Veuillez sélectionner une playlist', 'error');
        return;
    }

    if (savedPlaylists[name]) {
        currentPlaylist = [...savedPlaylists[name]];
        updatePlaylistDisplayImproved();
        showNotificationImproved(`Playlist "${name}" chargée avec succès`, 'success');
    } else {
        showNotificationImproved('Playlist introuvable', 'error');
    }
}

function deletePlaylistImproved() {
    const name = document.getElementById('playlist-select-improved').value;
    if (!name) {
        showNotificationImproved('Veuillez sélectionner une playlist', 'error');
        return;
    }

    if (confirm(`Êtes-vous sûr de vouloir supprimer la playlist "${name}" ?`)) {
        delete savedPlaylists[name];
        localStorage.setItem('savedPlaylists', JSON.stringify(savedPlaylists));
        updatePlaylistSelectImproved();
        showNotificationImproved(`Playlist "${name}" supprimée`, 'info');
    }
}

function updatePlaylistSelectImproved() {
    const select = document.getElementById('playlist-select-improved');
    select.innerHTML = '<option value="">Sélectionner une playlist...</option>';

    Object.keys(savedPlaylists).forEach(name => {
        const option = document.createElement('option');
        option.value = name;
        option.textContent = `${name} (${savedPlaylists[name].length} éléments)`;
        select.appendChild(option);
    });
}

// Contrôle audio global
let globalAudioEnabled = true;

function toggleGlobalAudio() {
    globalAudioEnabled = !globalAudioEnabled;
    const audioBtn = document.getElementById('audio-toggle');
    const icon = audioBtn.querySelector('i');

    if (globalAudioEnabled) {
        icon.className = 'fas fa-volume-up';
        audioBtn.title = 'Désactiver l\'audio';
        showNotificationImproved('Audio activé', 'success');
    } else {
        icon.className = 'fas fa-volume-mute';
        audioBtn.title = 'Activer l\'audio';
        showNotificationImproved('Audio désactivé', 'info');
    }

    // Appliquer le changement aux vidéos en cours
    document.querySelectorAll('video').forEach(video => {
        video.muted = !globalAudioEnabled;
    });
}

// Système de notifications amélioré
function showNotificationImproved(message, type = 'info') {
    // Supprimer les notifications existantes
    const existingNotifications = document.querySelectorAll('.notification-improved');
    existingNotifications.forEach(notif => notif.remove());

    const notification = document.createElement('div');
    notification.className = `notification-improved notification-${type}`;

    const icon = getNotificationIcon(type);
    notification.innerHTML = `
        <i class="${icon}"></i>
        <span>${message}</span>
        <button class="notification-close">&times;</button>
    `;

    // Ajouter les styles si nécessaire
    addNotificationStyles();

    document.body.appendChild(notification);

    // Animation d'entrée
    setTimeout(() => notification.classList.add('show'), 100);

    // Fermeture automatique
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    }, 4000);

    // Fermeture manuelle
    notification.querySelector('.notification-close').addEventListener('click', () => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    });
}

function getNotificationIcon(type) {
    switch (type) {
        case 'success': return 'fas fa-check-circle';
        case 'error': return 'fas fa-exclamation-circle';
        case 'warning': return 'fas fa-exclamation-triangle';
        default: return 'fas fa-info-circle';
    }
}

function addNotificationStyles() {
    if (document.getElementById('notification-styles')) return;

    const style = document.createElement('style');
    style.id = 'notification-styles';
    style.textContent = `
        .notification-improved {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            padding: 15px 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            min-width: 300px;
            border-left: 4px solid #007bff;
        }

        .notification-improved.show {
            transform: translateX(0);
        }

        .notification-success {
            border-left-color: #28a745;
            color: #155724;
        }

        .notification-error {
            border-left-color: #dc3545;
            color: #721c24;
        }

        .notification-warning {
            border-left-color: #ffc107;
            color: #856404;
        }

        .notification-info {
            border-left-color: #17a2b8;
            color: #0c5460;
        }

        .notification-close {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: inherit;
            opacity: 0.7;
            margin-left: auto;
        }

        .notification-close:hover {
            opacity: 1;
        }
    `;
    document.head.appendChild(style);
}

function closePlaylistManager() {
    const popup = document.getElementById('playlist-popup');
    if (popup) {
        popup.style.display = 'none';
    }
}

// Fonction pour obtenir la playlist actuelle (pour l'intégration avec le reste de l'application)
function getCurrentPlaylist() {
    return currentPlaylist;
}

// Fonction pour définir la playlist actuelle
function setCurrentPlaylist(playlist) {
    currentPlaylist = playlist || [];
    updatePlaylistDisplayImproved();
}

// Export des fonctions principales pour l'intégration
window.playlistManagerImproved = {
    initialize: initializeImprovedPlaylistManager,
    getCurrentPlaylist,
    setCurrentPlaylist,
    toggleGlobalAudio,
    showNotification: showNotificationImproved
};
