<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Widget Windguru</title>

</head>
<body>
<style>
  @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap');

  :root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --background-color: #ecf0f1;
    --text-color: #34495e;
  }

  body {
    font-family: 'Pilcrow Rounded', sans-serif;
    background-color: var(--background-color);
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    color: var(--text-color);
  }

  .container {
    background-color: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    max-width: 1200px;
    width: 95%;
  }

  h1 {
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 20px;
    font-weight: 700;
    font-size: 1.8rem;
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  .widget-container {
    border: 2px solid var(--primary-color);
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 20px;
    height: 600px;  /* Augmentation de la hauteur */
  }

  .info-section {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 20px;
  }

  .info-card {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 15px;
    border-radius: 10px;
    text-align: center;
    flex: 1;
    min-width: 150px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .info-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 7px 20px rgba(0, 0, 0, 0.2);
  }

  .info-card h3 {
    margin: 0;
    font-size: 1rem;
    font-weight: 400;
  }

  .info-card p {
    margin: 8px 0 0;
    font-size: 1.5rem;
    font-weight: 700;
  }

  .waves {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 15vh;
    margin-bottom: -7px;
    min-height: 100px;
    max-height: 150px;
    z-index: -1;
  }

  .parallax > use {
    animation: move-forever 25s cubic-bezier(.55,.5,.45,.5) infinite;
  }
  .parallax > use:nth-child(1) {
    animation-delay: -2s;
    animation-duration: 7s;
  }
  .parallax > use:nth-child(2) {
    animation-delay: -3s;
    animation-duration: 10s;
  }
  .parallax > use:nth-child(3) {
    animation-delay: -4s;
    animation-duration: 13s;
  }
  .parallax > use:nth-child(4) {
    animation-delay: -5s;
    animation-duration: 20s;
  }
  @keyframes move-forever {
    0% {
      transform: translate3d(-90px,0,0);
    }
    100% { 
      transform: translate3d(85px,0,0);
    }
  }
</style>

<div class="container">
  <h1>Prévisions Météo Marine</h1>
  <div id="widget-container">
<script id="wg_fwdg_48404_100_1722244908760">
  (function (window, document) {
    var loader = function () {
      var arg = ["s=48404" ,"m=100","mw=46","uid=wg_fwdg_48404_100_1722244908760" ,"wj=kmh" ,"tj=c" ,"waj=m" ,"tij=m" ,"odh=7" ,"doh=23" ,"fhours=78" ,"hrsm=1" ,"vt=forecasts" ,"lng=fr" ,"idbs=1" ,"ts=1" ,"p=TMPE,WINDSPD,GUST,SMER,HTSGW,PERPW,DIRPW,SWELL1,SWPER1,SWDIR1,WCHILL,CDC,APCP1s"];
      var script = document.createElement("script");
      var tag = document.getElementsByTagName("script")[0];
      script.src = "https://www.windguru.cz/js/widget.php?"+(arg.join("&"));
      tag.parentNode.insertBefore(script, tag);
    };
    window.addEventListener ? window.addEventListener("load", loader, false) : window.attachEvent("onload", loader);
  })(window, document);
  </script>
  </div>

</div>

<svg class="waves" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto">
<defs>
<path id="gentle-wave" d="M-160 44c30 0 58-18 88-18s 58 18 88 18 58-18 88-18 58 18 88 18 v44h-352z" />
</defs>
<g class="parallax">
<use xlink:href="#gentle-wave" x="48" y="0" fill="rgba(52, 152, 219,0.7" />
<use xlink:href="#gentle-wave" x="48" y="3" fill="rgba(52, 152, 219,0.5)" />
<use xlink:href="#gentle-wave" x="48" y="5" fill="rgba(52, 152, 219,0.3)" />
<use xlink:href="#gentle-wave" x="48" y="7" fill="rgba(52, 152, 219,0.1)" />
</g>
</svg>

<script>
// Fonction pour mettre à jour les valeurs des cartes d'information

</script>

</body></html>