const { app } = require('electron');
const path = require('path');
const fs = require('fs');

class ScheduleManager {
  constructor() {
    this.schedulesPath = path.join(app.getPath('userData'), 'schedules.json');
    this.schedules = this.loadSchedules();
    this.currentTimer = null;
    this.onPlaylistChange = null;
    this.mainWindow = null;
  }

  setMainWindow(window) {
    this.mainWindow = window;
  }

  // Ajout de la méthode manquante
  setPlaylistChangeCallback(callback) {
    this.onPlaylistChange = callback;
  }

  loadSchedules() {
    try {
      if (fs.existsSync(this.schedulesPath)) {
        const data = fs.readFileSync(this.schedulesPath, 'utf8');
        return JSON.parse(data);
      }
      return [];
    } catch (error) {
      console.error('Erreur lors du chargement des planifications:', error);
      return [];
    }
  }

  saveSchedules() {
    try {
      fs.writeFileSync(this.schedulesPath, JSON.stringify(this.schedules, null, 2));
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des planifications:', error);
    }
  }

  addSchedule(schedule) {
    const newSchedule = {
      id: Date.now().toString(),
      playlistName: schedule.playlistName,
      startTime: schedule.startTime,
      endTime: schedule.endTime,
      days: schedule.days,
      enabled: true
    };
    this.schedules.push(newSchedule);
    this.saveSchedules();
    this.checkAndUpdateSchedule();
  }

  removeSchedule(scheduleId) {
    this.schedules = this.schedules.filter(s => s.id !== scheduleId);
    this.saveSchedules();
    this.checkAndUpdateSchedule();
  }

  updateSchedule(scheduleId, updates) {
    this.schedules = this.schedules.map(schedule => {
      if (schedule.id === scheduleId) {
        return { ...schedule, ...updates };
      }
      return schedule;
    });
    this.saveSchedules();
    this.checkAndUpdateSchedule();
  }

  async checkAndUpdateSchedule() {
    if (this.currentTimer) {
      clearTimeout(this.currentTimer);
    }

    const now = new Date();
    const currentDay = now.getDay();
    const currentTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;

    // Trouve la planification active
    const activeSchedule = this.schedules.find(schedule => {
      if (!schedule.enabled || !schedule.days.includes(currentDay)) {
        return false;
      }

      const startTime = this.parseTime(schedule.startTime);
      const endTime = this.parseTime(schedule.endTime);
      const currentTimeMinutes = this.parseTime(currentTime);

      return currentTimeMinutes >= startTime && currentTimeMinutes <= endTime;
    });

    if (activeSchedule && this.mainWindow) {
      try {
        // Charger et définir la playlist active
        await this.mainWindow.webContents.executeJavaScript(`
          const savedPlaylists = JSON.parse(localStorage.getItem('savedPlaylists') || '{}');
          const playlist = savedPlaylists['${activeSchedule.playlistName}'];
          if (playlist) {
            currentPlaylist = [...playlist];
            if (typeof updatePlaylistDisplay === 'function') {
              updatePlaylistDisplay();
            }
          }
        `);

        if (this.onPlaylistChange) {
          this.onPlaylistChange(activeSchedule.playlistName);
        }
      } catch (error) {
        console.error('Erreur lors du changement de playlist:', error);
      }
    }

    // Planifie la prochaine vérification
    this.scheduleNextCheck();
  }

  parseTime(timeString) {
    const [hours, minutes] = timeString.split(':').map(Number);
    return hours * 60 + minutes;
  }

  scheduleNextCheck() {
    this.currentTimer = setTimeout(() => {
      this.checkAndUpdateSchedule();
    }, 60000); // Vérifie toutes les minutes
  }

  getSchedules() {
    return this.schedules;
  }

  start() {
    this.checkAndUpdateSchedule();
  }

  stop() {
    if (this.currentTimer) {
      clearTimeout(this.currentTimer);
    }
  }
}

module.exports = ScheduleManager;