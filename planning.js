let schedules = [];

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', async () => {
    const scheduleButton = document.getElementById('open-schedule');
    const schedulePopup = document.getElementById('schedule-popup');
    const closeButton = schedulePopup.querySelector('.close');

    scheduleButton.addEventListener('click', async () => {
        await loadPlaylists();
        schedulePopup.style.display = 'block';
    });

    closeButton.addEventListener('click', () => {
        schedulePopup.style.display = 'none';
    });

    window.addEventListener('click', (event) => {
        if (event.target === schedulePopup) {
            schedulePopup.style.display = 'none';
        }
    });

    // Charger les planifications existantes
    try {
        schedules = await window.electronAPI.getSchedules();
        updateSchedulesList();
    } catch (error) {
        console.error('Erreur lors du chargement des planifications:', error);
    }

    // Ajouter le gestionnaire pour l'ajout de planification
    document.getElementById('add-schedule').addEventListener('click', addSchedule);
});

// Charge les playlists dans le sélecteur
async function loadPlaylists() {
  try {
    const playlistSelect = document.getElementById('playlist-schedule-select');
    const savedPlaylists = JSON.parse(localStorage.getItem('savedPlaylists') || '{}');
    
    playlistSelect.innerHTML = '<option value="">Sélectionnez une playlist</option>';
    
    Object.keys(savedPlaylists).forEach(playlistName => {
      const option = document.createElement('option');
      option.value = playlistName;
      option.textContent = playlistName;
      playlistSelect.appendChild(option);
    });

    if (Object.keys(savedPlaylists).length === 0) {
      const option = document.createElement('option');
      option.disabled = true;
      option.textContent = 'Aucune playlist disponible';
      playlistSelect.appendChild(option);
    }
  } catch (error) {
    console.error('Erreur lors du chargement des playlists:', error);
    showNotification('Erreur lors du chargement des playlists');
  }
}

// Mise à jour de la liste des planifications
function updateSchedulesList() {
  const container = document.getElementById('schedules-container');
  container.innerHTML = schedules.map(schedule => `
<div class="schedule-item">
    <div class="schedule-info">
        <strong>${schedule.playlistName}</strong>
        <span class="schedule-time">${schedule.startTime} - ${schedule.endTime}</span>
        <span class="schedule-days">${formatDays(schedule.days)}</span>
    </div>
    <div class="schedule-actions">
        <div class="toggle-schedule ${schedule.enabled ? 'active' : ''}"
             onclick="toggleSchedule('${schedule.id}')"></div>
        <button class="btn-delete-schedule" onclick="deleteSchedule('${schedule.id}')">
            <span class="material-icons">delete</span>
        </button>
    </div>
</div>
  `).join('');
}

// Ajout d'une planification
async function addSchedule() {
  const playlistName = document.getElementById('playlist-schedule-select').value;
  const startTime = document.getElementById('start-time').value;
  const endTime = document.getElementById('end-time').value;
  const days = Array.from(document.querySelectorAll('.days-checkboxes input:checked'))
    .map(checkbox => parseInt(checkbox.value));

  if (!playlistName || !startTime || !endTime || days.length === 0) {
    showNotification('Veuillez remplir tous les champs');
    return;
  }

  try {
    await window.electronAPI.addSchedule({ playlistName, startTime, endTime, days });
    schedules = await window.electronAPI.getSchedules();
    updateSchedulesList();
    showNotification('Planification ajoutée avec succès');
  } catch (error) {
    console.error('Erreur lors de l\'ajout de la planification:', error);
    showNotification('Erreur lors de l\'ajout de la planification');
  }
}

// Suppression d'une planification
async function deleteSchedule(scheduleId) {
  try {
    await window.electronAPI.removeSchedule(scheduleId);
    schedules = await window.electronAPI.getSchedules();
    updateSchedulesList();
    showNotification('Planification supprimée');
  } catch (error) {
    console.error('Erreur lors de la suppression:', error);
    showNotification('Erreur lors de la suppression');
  }
}

// Activation/désactivation d'une planification
async function toggleSchedule(scheduleId) {
  const schedule = schedules.find(s => s.id === scheduleId);
  if (schedule) {
    try {
      await window.electronAPI.updateSchedule(scheduleId, { enabled: !schedule.enabled });
      schedules = await window.electronAPI.getSchedules();
      updateSchedulesList();
    } catch (error) {
      console.error('Erreur lors de la mise à jour:', error);
      showNotification('Erreur lors de la mise à jour');
    }
  }
}

// Formatage des jours pour l'affichage
function formatDays(days) {
  const dayNames = ['Dim', 'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam'];
  return days.map(day => dayNames[day]).join(', ');
}

// Affichage des notifications
function showNotification(message, type = 'success') {
  const notification = document.getElementById('notification');
  if (notification) {
    notification.textContent = message;
    notification.className = `notification ${type}`;
    notification.style.display = 'block';
    
    setTimeout(() => {
      notification.style.display = 'none';
    }, 3000);
  }
}

// Rendre les fonctions disponibles globalement
window.deleteSchedule = deleteSchedule;
window.toggleSchedule = toggleSchedule;