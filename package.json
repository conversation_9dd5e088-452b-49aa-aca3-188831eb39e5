{"name": "nautiflix", "productName": "Nautiflix", "version": "1.0.0", "description": "Nautical Digital Signage app", "main": "main.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "electron .", "make": "electron-forge make", "postinstall": "node postInstall.js", "package": "electron-forge package"}, "author": "<PERSON> - <PERSON>im<PERSON>", "license": "ISC", "devDependencies": {"@electron-forge/cli": "^7.4.0", "@electron-forge/maker-squirrel": "^7.4.0", "@electron-forge/plugin-auto-unpack-natives": "^7.4.0", "@electron-forge/plugin-fuses": "^7.4.0", "@electron/fuses": "^1.8.0", "@electron/packager": "^18.3.3", "electron": "^31.3.0"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.6.0", "axios": "^1.7.2", "custom-electron-titlebar": "^4.2.8", "electron-is-dev": "^3.0.1", "electron-squirrel-startup": "^1.0.1", "electron-util": "^0.18.1", "puppeteer": "^22.15.0", "update-electron-app": "^3.0.0", "video.js": "^8.17.1"}}