// Configuration de l'API Open-Meteo
const WEATHER_API_URL = 'https://api.open-meteo.com/v1/forecast';
const LATITUDE = 49;
const LONGITUDE = -1.6;

// Fonction pour obtenir le chemin de l'icône en fonction du code WMO
function getWeatherInfo(code, isDay = true) {
  console.log('Code météo WMO reçu:', code);
  
  const weatherCodes = {
    0: {
      description: 'Ciel dégagé',
      icon: isDay ? 'clear-day.svg' : 'clear-night.svg'
    },
    1: {
      description: 'Principalement dégagé',
      icon: isDay ? 'partly-cloudy-day.svg' : 'partly-cloudy-night.svg'
    },
    2: {
      description: 'Partiellement nuageux',
      icon: isDay ? 'partly-cloudy-day.svg' : 'partly-cloudy-night.svg'
    },
    3: {
      description: 'Couvert',
      icon: 'cloudy.svg'
    },
    45: {
      description: 'Brouillard',
      icon: 'fog.svg'
    },
    48: {
      description: 'Brouillard givrant',
      icon: 'fog.svg'
    },
    51: {
      description: 'Bruine légère',
      icon: 'drizzle.svg'
    },
    53: {
      description: 'Bruine modérée',
      icon: 'drizzle.svg'
    },
    55: {
      description: 'Bruine dense',
      icon: 'drizzle.svg'
    },
    61: {
      description: 'Pluie légère',
      icon: 'rain.svg'
    },
    63: {
      description: 'Pluie modérée',
      icon: 'rain.svg'
    },
    65: {
      description: 'Pluie forte',
      icon: 'rain-strong.svg'
    },
    71: {
      description: 'Neige légère',
      icon: 'snow.svg'
    },
    73: {
      description: 'Neige modérée',
      icon: 'snow.svg'
    },
    75: {
      description: 'Neige forte',
      icon: 'snow.svg'
    },
    77: {
      description: 'Grains de neige',
      icon: 'snow.svg'
    },
    80: {
      description: 'Averses légères',
      icon: 'rain.svg'
    },
    81: {
      description: 'Averses modérées',
      icon: 'rain.svg'
    },
    82: {
      description: 'Averses violentes',
      icon: 'rain.svg'
    },
    95: {
      description: 'Orage',
      icon: 'thunderstorms.svg'
    }
  };
  
  const defaultWeatherInfo = { 
    description: 'Indéterminé', 
    icon: 'unknown.svg' 
  };

  const weatherInfo = weatherCodes[code] || defaultWeatherInfo;
  console.log('Description météo:', weatherInfo.description);
  console.log('Icône sélectionnée:', weatherInfo.icon);
  
  return weatherInfo;
}

// Fonction pour déterminer si c'est le jour ou la nuit
function isDaytime(sunrise, sunset) {
  const now = new Date();
  const sunriseTime = new Date(sunrise);
  const sunsetTime = new Date(sunset);
  return now >= sunriseTime && now < sunsetTime;
}

// Fonction pour convertir la direction du vent en texte
function getWindDirection(degrees) {
  const directions = ['N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE', 'S', 'SSO', 'SO', 'OSO', 'O', 'ONO', 'NO', 'NNO'];
  const index = Math.round(degrees / 22.5) % 16;
  return directions[index];
}

async function updateWeather() {
  try {
    const params = new URLSearchParams({
      latitude: LATITUDE,
      longitude: LONGITUDE,
      daily: [
        'weather_code',
        'temperature_2m_max',
        'temperature_2m_min',
        'sunrise',
        'sunset',
        'wind_speed_10m_max',
        'wind_gusts_10m_max',
        'wind_direction_10m_dominant'
      ].join(','),
      wind_speed_unit: 'kn', // Vitesse du vent directement en nœuds
      timezone: 'auto',
      forecast_days: 1,
      models: 'meteofrance_seamless'
    });

    const response = await fetch(`${WEATHER_API_URL}?${params}`);
    const data = await response.json();
    console.log('Données météo reçues:', data);

    if (data.daily) {
      // Extraction des données du jour
      const daily = {
        weatherCode: data.daily.weather_code[0],
        tempMax: data.daily.temperature_2m_max[0],
        tempMin: data.daily.temperature_2m_min[0],
        windSpeed: data.daily.wind_speed_10m_max[0], // Déjà en nœuds
        windGusts: data.daily.wind_gusts_10m_max[0], // Déjà en nœuds
        windDirection: data.daily.wind_direction_10m_dominant[0],
        sunrise: new Date(data.daily.sunrise[0]),
        sunset: new Date(data.daily.sunset[0])
      };

      console.log('Données journalières extraites:', daily);

      // Vérifier si c'est le jour ou la nuit
      const isDay = isDaytime(daily.sunrise, daily.sunset);
      console.log('Période de la journée:', isDay ? 'Jour' : 'Nuit');

      // Obtenir les informations météo et le chemin de l'icône
      const weatherInfo = getWeatherInfo(daily.weatherCode, isDay);
      const iconPath = `assets/iconemeteo/${weatherInfo.icon}`;
      console.log('Chemin de l\'icône:', iconPath);

      // Obtenir la direction du vent
      const windDir = getWindDirection(daily.windDirection);

      // Mise à jour de l'interface utilisateur
      const weatherIcon = document.getElementById('weather-icon');
      weatherIcon.src = iconPath;
      weatherIcon.alt = weatherInfo.description;

      document.getElementById('weather-temperature').textContent = 
        `Min : ${daily.tempMin.toFixed(1)}°C - Max: ${daily.tempMax.toFixed(1)}°C`;
      document.getElementById('weather-wind').textContent = 
        `Vent max: ${daily.windSpeed.toFixed(1)} kn (${windDir})`;
      document.getElementById('weather-visibility').textContent = 
        `Rafales max: ${daily.windGusts.toFixed(1)} kn`;

      // Formatage des heures de lever/coucher du soleil
      const sunriseTime = daily.sunrise.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
      const sunsetTime = daily.sunset.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
      
      // Mise à jour des heures de lever/coucher du soleil si les éléments existent
      const sunriseElement = document.getElementById('sunrise-time');
      const sunsetElement = document.getElementById('sunset-time');
      
      if (sunriseElement) sunriseElement.textContent = sunriseTime;
      if (sunsetElement) sunsetElement.textContent = sunsetTime;

    } else {
      throw new Error('Invalid weather data');
    }
  } catch (error) {
    console.error('Erreur lors de la récupération des données météo:', error);
    document.getElementById('weather-info').textContent = 'Météo non disponible';
  }
}

function updateDateTime() {
  const now = new Date();
  const dateTimeString = now.toLocaleString('fr-FR', { 
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
  document.getElementById('date-time').textContent = dateTimeString;
}

// Mise à jour de la date, de l'heure et de la météo
setInterval(updateDateTime, 1000);
setInterval(updateWeather, 300000); // Mise à jour toutes les 5 minutes

// Mise à jour initiale
updateDateTime();
updateWeather();