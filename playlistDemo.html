<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nautiflix - Gestionnaire de Playlist Amélioré</title>
    
    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .demo-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 40px;
            text-align: center;
            max-width: 600px;
            width: 100%;
        }
        
        .demo-title {
            color: #333;
            margin-bottom: 20px;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .demo-subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 1.2em;
        }
        
        .demo-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 18px;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        
        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }
        
        .features-list {
            text-align: left;
            margin: 30px 0;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
        }
        
        .features-list h3 {
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .features-list ul {
            list-style: none;
        }
        
        .features-list li {
            padding: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .features-list li i {
            color: #28a745;
            width: 20px;
        }
        
        #playlist-popup {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            backdrop-filter: blur(5px);
        }
        
        .playlist-popup-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            width: 90%;
            max-width: 1000px;
            max-height: 90vh;
            overflow: hidden;
        }
        
        .audio-status {
            margin-top: 20px;
            padding: 15px;
            background: #e3f2fd;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
        }
        
        .audio-status h4 {
            color: #1976d2;
            margin-bottom: 10px;
        }
        
        .audio-status p {
            color: #424242;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">
            <i class="fas fa-anchor"></i>
            Nautiflix
        </h1>
        <p class="demo-subtitle">Gestionnaire de Playlist Amélioré</p>
        
        <div class="features-list">
            <h3><i class="fas fa-star"></i> Nouvelles Fonctionnalités</h3>
            <ul>
                <li><i class="fas fa-check"></i> Interface moderne et intuitive</li>
                <li><i class="fas fa-check"></i> Drag & Drop pour les fichiers</li>
                <li><i class="fas fa-check"></i> Prévisualisation des médias</li>
                <li><i class="fas fa-check"></i> Contrôle audio global</li>
                <li><i class="fas fa-check"></i> Réorganisation facile des éléments</li>
                <li><i class="fas fa-check"></i> Sauvegarde/Chargement amélioré</li>
                <li><i class="fas fa-check"></i> Notifications en temps réel</li>
                <li><i class="fas fa-check"></i> Support multi-formats</li>
            </ul>
        </div>
        
        <div class="audio-status">
            <h4><i class="fas fa-volume-up"></i> Correction Audio</h4>
            <p>
                Le problème audio a été corrigé ! Les vidéos ne sont plus en mode muet par défaut.
                Vous pouvez maintenant contrôler l'audio globalement depuis l'interface de playlist.
            </p>
        </div>
        
        <button class="demo-button" onclick="openPlaylistManager()">
            <i class="fas fa-play"></i>
            Ouvrir le Gestionnaire de Playlist
        </button>
    </div>
    
    <!-- Popup de la playlist -->
    <div id="playlist-popup">
        <div class="playlist-popup-content">
            <!-- Le contenu sera généré par playlistManagerImproved.js -->
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="playlistManagerImproved.js"></script>
    
    <script>
        function openPlaylistManager() {
            const popup = document.getElementById('playlist-popup');
            popup.style.display = 'block';
            
            // Initialiser le gestionnaire de playlist amélioré
            if (window.playlistManagerImproved) {
                window.playlistManagerImproved.initialize();
            }
        }
        
        // Fermer la popup en cliquant à l'extérieur
        document.getElementById('playlist-popup').addEventListener('click', function(e) {
            if (e.target === this) {
                this.style.display = 'none';
            }
        });
        
        // Exemple de données de test
        window.addEventListener('load', function() {
            // Ajouter quelques playlists de démonstration
            const demoPlaylists = {
                'Playlist Nautique': [
                    {
                        id: 1,
                        type: 'video',
                        name: 'Vidéo de voile.mp4',
                        content: 'data:video/mp4;base64,demo',
                        duration: 30000,
                        size: 1024000
                    },
                    {
                        id: 2,
                        type: 'image',
                        name: 'Coucher de soleil.jpg',
                        content: 'data:image/jpeg;base64,demo',
                        duration: 10000,
                        size: 512000
                    }
                ],
                'Playlist Régates': [
                    {
                        id: 3,
                        type: 'webpage',
                        name: 'Résultats de course',
                        content: 'https://example.com/results',
                        duration: 15000,
                        size: 0
                    }
                ]
            };
            
            localStorage.setItem('savedPlaylists', JSON.stringify(demoPlaylists));
        });
        
        // Gestion des raccourcis clavier
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const popup = document.getElementById('playlist-popup');
                if (popup.style.display === 'block') {
                    popup.style.display = 'none';
                }
            }
        });
    </script>
</body>
</html>
