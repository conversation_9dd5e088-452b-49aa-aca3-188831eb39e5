<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calendrier des Marées</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f2f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        select {
            padding: 8px;
            margin: 10px 0;
            border-radius: 4px;
            border: 1px solid #ccc;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background-color: white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .tide-cell {
            border-radius: 4px;
            padding: 8px;
            margin: 4px;
            display: inline-block;
        }
        .loading {
            text-align: center;
            padding: 20px;
            font-size: 1.2em;
        }
        .error {
            color: red;
            padding: 20px;
            text-align: center;
        }
        /* Couleurs pour les hauteurs */
        .height-12-plus { background-color: #1e40af; color: white; }
        .height-10-12 { background-color: #2563eb; color: white; }
        .height-8-10 { background-color: #3b82f6; color: white; }
        .height-6-8 { background-color: #60a5fa; color: white; }
        .height-4-6 { background-color: #93c5fd; }
        .height-0-4 { background-color: #dbeafe; }
        #errorDetails {
            background: #fee2e2;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Calendrier des Marées</h1>
        <select id="monthSelect">
            <option value="11">Novembre</option>
            <option value="12">Décembre</option>
            <option value="01">Janvier</option>
            <option value="02">Février</option>
        </select>
        <div id="errorDetails"></div>
        <div id="tidesTable"></div>
    </div>

    <script>
        let tidesData = [];
        const CORS_PROXY = 'https://cors-anywhere.herokuapp.com/';
        const GITHUB_URL = 'https://raw.githubusercontent.com/patricklouvel/nautiflixData/main/TidesCalendar.json';
        
        // Données de test au cas où l'API ne fonctionne pas
        const testData = [
            {
                "date": "01/11/2024",
                "tides": [
                    { "time": "01h52", "height": 2.24 },
                    { "time": "07h00", "height": 12.13 },
                    { "time": "14h07", "height": 2.21 },
                    { "time": "19h14", "height": 12.20 }
                ],
                "coefficient": [81, 82]
            }
            // Ajoutez d'autres jours si nécessaire
        ];

        function getHeightClass(height) {
            if (height >= 12) return 'height-12-plus';
            if (height >= 10) return 'height-10-12';
            if (height >= 8) return 'height-8-10';
            if (height >= 6) return 'height-6-8';
            if (height >= 4) return 'height-4-6';
            return 'height-0-4';
        }

        function displayTides(month) {
            const filteredData = tidesData.filter(day => day.date.split('/')[1] === month);
            const tableHtml = `
                <table>
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Marée 1</th>
                            <th>Marée 2</th>
                            <th>Marée 3</th>
                            <th>Marée 4</th>
                            <th>Coefficient</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${filteredData.map(day => `
                            <tr>
                                <td>${day.date}</td>
                                ${day.tides.map(tide => `
                                    <td>
                                        <div class="tide-cell ${getHeightClass(tide.height)}">
                                            ${tide.time}<br>
                                            ${tide.height.toFixed(2)}m
                                        </div>
                                    </td>
                                `).join('')}
                                ${Array(4 - day.tides.length).fill('<td>-</td>').join('')}
                                <td>${day.coefficient.join(' - ')}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
            document.getElementById('tidesTable').innerHTML = tableHtml;
        }

        async function fetchData(url) {
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return await response.json();
        }

        async function init() {
            document.getElementById('tidesTable').innerHTML = '<div class="loading">Chargement des données...</div>';
            
            try {
                // Essayer d'abord l'URL directe
                tidesData = await fetchData(GITHUB_URL);
                document.getElementById('errorDetails').style.display = 'none';
            } catch (error1) {
                console.log('Première tentative échouée, essai avec proxy CORS...');
                try {
                    // Si ça échoue, essayer avec le proxy CORS
                    tidesData = await fetchData(CORS_PROXY + GITHUB_URL);
                    document.getElementById('errorDetails').style.display = 'none';
                } catch (error2) {
                    console.log('Deuxième tentative échouée, utilisation des données de test...');
                    // Si les deux méthodes échouent, utiliser les données de test
                    tidesData = testData;
                    document.getElementById('errorDetails').style.display = 'block';
                    document.getElementById('errorDetails').innerHTML = 
                        `Note: Utilisation des données de test car les données en ligne sont inaccessibles.<br>
                        Erreur: ${error2.message}`;
                }
            }
            
            displayTides(document.getElementById('monthSelect').value);
        }

        document.getElementById('monthSelect').addEventListener('change', (e) => {
            displayTides(e.target.value);
        });

        // Initialiser l'application
        init();
    </script>
</body>
</html>


