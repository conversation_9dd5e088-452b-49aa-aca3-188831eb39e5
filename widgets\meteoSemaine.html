<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Prévisions météo à 5 jours</title>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/weather-icons/2.0.12/css/weather-icons.min.css"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-family: "Segoe UI", system-ui, -apple-system, sans-serif;
      }

      body {
        background: linear-gradient(135deg, #0e4e74 0%, #05316b 100%);
        max-width: calc(100vw - 260px);
        min-height: 100vh;
        margin: 0;
        padding: 20px;
        color: #e6e6e6;
        overflow-x: hidden;
      }

      h1 {
        font-size: 2.2em;
        padding-bottom: 10px;
        margin: 0 0 10px 0;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
      }



      .weather-container-wrapper {
        width: 100%;
        overflow-x: auto;
        padding: 10px 0px;
        scrollbar-width: none;
        -ms-overflow-style: none;
        mask-image: linear-gradient(
          90deg,
          transparent,
          #000 5%,
          #000 95%,
          transparent
        );
        -webkit-mask-image: linear-gradient(
          90deg,
          transparent,
          #000 5%,
          #000 95%,
          transparent
        );
      }

      .weather-container-wrapper::-webkit-scrollbar {
        display: none;
      }

      .weather-container {
        display: flex;
        gap: 15px;
        padding: 0px;
        width: max-content;
        margin: 0 auto;
      }

      .weather-card {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 20px;
        padding: 20px;
        backdrop-filter: blur(10px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        width: 250px;
        flex-shrink: 0;
        animation: fadeIn 0.5s ease-in-out;
      }

      .weather-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.2);
        background: rgba(255, 255, 255, 0.08);
      }

      .date {
        text-align: center;
        font-size: 1.1rem;
        font-weight: 500;
        color: #ffffff;
        margin-bottom: 20px;
        text-transform: capitalize;
        letter-spacing: 0.5px;
      }

      .weather-icon {
        text-align: center;
        margin: 20px 0;
      }

      .weather-icon i {
        font-size: 4em;
        color: #ffffff;
        text-shadow: 0 0 20px rgba(255, 255, 255, 0.4);
        background: linear-gradient(135deg, #64b5f6, #2196f3);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .weather-desc {
        text-align: center;
        font-size: 1rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 20px;
        min-height: 2.4em;
        font-weight: 300;
        letter-spacing: 0.5px;
      }

      .temp {
        font-size: 1.8em;
        color: #ffffff;
        margin: 15px 0;
        text-align: center;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        font-weight: 500;
      }

      .details {
        background: rgba(255, 255, 255, 0.03);
        border-radius: 15px;
        padding: 20px;
        margin-top: 20px;
        border: 1px solid rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(5px);
      }

      .sun-times {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }

      .sun-times i {
        font-size: 1.4em;
        margin-right: 8px;
        color: #bec07d;
        vertical-align: middle;
      }

      .sun-times span {
        font-size: 0.95rem;
        display: flex;
        align-items: center;
      }

      .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 12px 0;
        font-size: 0.95rem;
      }

      .info-label {
        color: rgba(255, 255, 255, 0.8);
        font-weight: 300;
      }

      .value {
        padding: 4px 12px;
        border-radius: 20px;
        background: rgba(255, 255, 255, 0.08);
        font-size: 0.9rem;
        letter-spacing: 0.5px;
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      .tides-section {
        background: rgba(255, 255, 255, 0.03);
        border-radius: 15px;
        padding: 20px;
        margin-top: 20px;
        border: 1px solid rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(5px);
      }

      .tides-title {
        font-size: 1rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 15px;
        text-align: center;
        font-weight: 400;
        letter-spacing: 0.5px;
        position: relative;
      }

      .tide-row {
        margin: 10px 0;
        border-radius: 12px;
        padding: 12px;
        transition: all 0.3s ease;
      }

      .tide-row:hover {
        transform: scale(1.02);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      }

      .tide-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: white;
      }

      .tide-time {
        font-weight: 500;
        font-size: 0.95rem;
        letter-spacing: 0.5px;
      }

      .tide-height {
        font-size: 0.9rem;
        padding: 3px 8px;
        border-radius: 10px;
        background: rgba(255, 255, 255, 0.1);
      }

      .height-12-plus {
        background: linear-gradient(135deg, #081773, #020b6a);
      }
      .height-10-12 {
        background: linear-gradient(135deg, #0e227c, #081773);
      }
      .height-8-10 {
        background: linear-gradient(135deg, #2c5aac, #0e227c);
      }
      .height-6-8 {
        background: linear-gradient(135deg, #3f7bca, #2c5aac);
      }
      .height-4-6 {
        background: linear-gradient(135deg, #4795d3, #3f7bca);
      }
      .height-0-4 {
        background: linear-gradient(135deg, #5ec2f5, #4795d3);
      }

      /* Et pour s'assurer que tous les éléments dans cette classe sont lisibles */
      .height-0-4 .tide-time,
      .height-0-4 .tide-height {
        color: #03233d; /* Applique la couleur foncée aussi aux sous-éléments */
      }
      .height-4-6 .tide-time,
      .height-4-6 .tide-height {
        color: #03233d; /* Applique la couleur foncée aussi aux sous-éléments */
      }
      .height-6-8 .tide-time,
      .height-6-8 .tide-height {
        color: #03233d; /* Applique la couleur foncée aussi aux sous-éléments */
      }

      .coefficient-info {
        text-align: center;
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        color: #ffd54f;
        font-size: 0.95rem;
        letter-spacing: 0.5px;
        font-weight: 300;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @media (max-width: 768px) {
        .weather-card {
          width: 240px;
          padding: 20px;
        }
      }
    </style>
  </head>
  <body>
    <h1>Conditions maritimes à 5 jours</h1>
    <div class="weather-container-wrapper">
      <div id="weatherContainer" class="weather-container"></div>
    </div>

    <script>
      const GITHUB_URL =
        "https://raw.githubusercontent.com/patricklouvel/nautiflixData/main/TidesCalendar.json";

      function getHeightClass(height) {
        if (height >= 12) return "height-12-plus";
        if (height >= 10) return "height-10-12";
        if (height >= 8) return "height-8-10";
        if (height >= 6) return "height-6-8";
        if (height >= 4) return "height-4-6";
        return "height-0-4";
      }

      function getWeatherInfo(code) {
        const weatherCodes = {
          0: { icon: "wi wi-day-sunny", desc: "Ciel dégagé" },
          1: { icon: "wi wi-day-cloudy", desc: "Principalement dégagé" },
          2: { icon: "wi wi-cloudy", desc: "Partiellement nuageux" },
          3: { icon: "wi wi-cloudy", desc: "Nuageux" },
          45: { icon: "wi wi-fog", desc: "Brouillard" },
          48: { icon: "wi wi-fog", desc: "Brouillard givrant" },
          51: { icon: "wi wi-sprinkle", desc: "Bruine légère" },
          53: { icon: "wi wi-sprinkle", desc: "Bruine modérée" },
          55: { icon: "wi wi-sprinkle", desc: "Bruine dense" },
          61: { icon: "wi wi-rain", desc: "Pluie légère" },
          63: { icon: "wi wi-rain", desc: "Pluie modérée" },
          65: { icon: "wi wi-rain", desc: "Pluie forte" },
          71: { icon: "wi wi-snow", desc: "Neige légère" },
          73: { icon: "wi wi-snow", desc: "Neige modérée" },
          75: { icon: "wi wi-snow", desc: "Neige forte" },
        };
        return weatherCodes[code] || { icon: "wi wi-na", desc: "Inconnu" };
      }

      function formatDate(dateStr) {
        const date = new Date(dateStr);
        const options = { weekday: "long", day: "numeric" };
        return new Intl.DateTimeFormat("fr-FR", options).format(date);
      }

      function formatTime(timeStr) {
        const time = new Date(timeStr);
        return time.toLocaleTimeString("fr-FR", {
          hour: "2-digit",
          minute: "2-digit",
        });
      }

      function formatDateForJson(date) {
        const [year, month, day] = date.split("-");
        return `${day}/${month}/${year}`;
      }

      function generateTidesHTML(tideData) {
        if (!tideData) return "";

        let html = '<div class="tides-section">';
        html += '<div class="tides-title">Marées</div>';

        tideData.tides.forEach((tide) => {
          const heightClass = getHeightClass(tide.height);
          html += `
            <div class="tide-row ${heightClass}">
                <div class="tide-info">
                    <span class="tide-time">${tide.time}</span>
                    <span class="tide-height">${tide.height.toFixed(2)}m</span>
                </div>
            </div>
        `;
        });

        if (tideData.coefficient && tideData.coefficient.length > 0) {
          html += `
            <div class="coefficient-info">
                Coef. ${tideData.coefficient.join(" / ")}
            </div>
        `;
        }

        html += "</div>";
        return html;
      }

      function displayWeatherAndTides(weatherData, tidesData) {
        const container = document.getElementById("weatherContainer");
        container.innerHTML = ""; // Vider le conteneur d'abord

        // Prendre les indices 1 à 5 (demain + 4 jours suivants)
        const startIndex = 1; // On commence à l'index 1 (demain)
        const numberOfDays = 5;
        const endIndex = startIndex + numberOfDays;

        // Extraire les données pour les 5 prochains jours à partir de demain
        for (let i = startIndex; i < endIndex; i++) {
          const weather = getWeatherInfo(weatherData.daily.weather_code[i]);
          const date = weatherData.daily.time[i];
          const formattedDate = formatDateForJson(date);
          const tideInfo = tidesData.find((t) => t.date === formattedDate);

          const card = document.createElement("div");
          card.className = "weather-card";

          card.innerHTML = `
            <div class="date">${formatDate(date)}</div>
            <div class="weather-icon">
                <i class="${weather.icon}" title="${weather.desc}"></i>
            </div>
            <div class="weather-desc">${weather.desc}</div>
            <div class="temp">
                ${Math.round(weatherData.daily.temperature_2m_min[i])}° / 
                ${Math.round(weatherData.daily.temperature_2m_max[i])}°
            </div>
            <div class="details">
                <div class="sun-times">
                    <span><i class="wi wi-sunrise"></i> ${formatTime(
                      weatherData.daily.sunrise[i]
                    )}</span>
                    <span><i class="wi wi-sunset"></i> ${formatTime(
                      weatherData.daily.sunset[i]
                    )}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Préc.</span>
                    <span class="value">${
                      weatherData.daily.precipitation_probability_max[i]
                    }%</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Vent</span>
                    <span class="value">${Math.round(
                      weatherData.daily.wind_speed_10m_max[i]
                    )}kn</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Raf.</span>
                    <span class="value">${Math.round(
                      weatherData.daily.wind_gusts_10m_max[i]
                    )}kn</span>
                </div>
            </div>
            ${generateTidesHTML(tideInfo)}
        `;

          container.appendChild(card);
        }
      }

      // Modifier l'appel à l'API pour récupérer 6 jours (aujourd'hui + 5 jours)
      async function loadData() {
        try {
          const [weatherResponse, tidesResponse] = await Promise.all([
            fetch(
              "https://api.open-meteo.com/v1/forecast?latitude=49&longitude=-1.6&daily=weather_code,temperature_2m_max,temperature_2m_min,sunrise,sunset,precipitation_hours,precipitation_probability_max,wind_speed_10m_max,wind_gusts_10m_max&wind_speed_unit=kn&timezone=auto&forecast_days=6"
            ),
            fetch(GITHUB_URL),
          ]);

          if (!weatherResponse.ok || !tidesResponse.ok)
            throw new Error("Problème de récupération des données");

          const weatherData = await weatherResponse.json();
          const tidesData = await tidesResponse.json();

          displayWeatherAndTides(weatherData, tidesData);
        } catch (error) {
          console.error(error);
          document.getElementById("weatherContainer").innerHTML =
            "<p>Erreur de chargement des données météo ou marée.</p>";
        }
      }

      loadData();

      document.addEventListener("DOMContentLoaded", loadData);
    </script>
  </body>
</html>
