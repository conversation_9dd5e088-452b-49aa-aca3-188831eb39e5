<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Éditeurs Nautiflix - Electron</title>
    
    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Styles des éditeurs -->
    <link rel="stylesheet" href="editorsStyles.css">
    
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #2c5aa0 0%, #87ceeb 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            margin: 0;
        }
        
        .test-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 40px;
            text-align: center;
            max-width: 600px;
            width: 100%;
        }
        
        .test-title {
            color: #2c5aa0;
            margin-bottom: 20px;
            font-size: 2em;
            font-weight: 300;
        }
        
        .test-buttons {
            display: flex;
            gap: 20px;
            margin: 30px 0;
            justify-content: center;
        }
        
        .test-btn {
            padding: 15px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            color: white;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .meteo-test-btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        }
        
        .info-test-btn {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }
        
        .debug-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            text-align: left;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">
            <i class="fas fa-anchor"></i>
            Test Éditeurs Nautiflix
        </h1>
        
        <p>Test des éditeurs intégrés dans l'environnement Electron</p>
        
        <div class="test-buttons">
            <button id="test-meteo-btn" class="test-btn meteo-test-btn">
                <i class="fas fa-cloud-sun"></i>
                Test Éditeur Météo
            </button>
            
            <button id="test-info-btn" class="test-btn info-test-btn">
                <i class="fas fa-file-alt"></i>
                Test Éditeur Pages
            </button>
        </div>
        
        <div id="status-container">
            <!-- Les statuts apparaîtront ici -->
        </div>
        
        <div class="debug-info" id="debug-log">
            <strong>Log de débogage :</strong><br>
            Chargement de la page...<br>
        </div>
    </div>
    
    <!-- Scripts des éditeurs -->
    <script>
        // Fonction de log pour le débogage
        function debugLog(message, type = 'info') {
            const logElement = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}<br>`;
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(`[${timestamp}] ${message}`);
        }
        
        // Fonction pour afficher le statut
        function showStatus(message, type = 'info') {
            const container = document.getElementById('status-container');
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            container.appendChild(statusDiv);
            
            // Supprimer après 5 secondes
            setTimeout(() => {
                if (statusDiv.parentNode) {
                    statusDiv.parentNode.removeChild(statusDiv);
                }
            }, 5000);
        }
        
        debugLog('Page chargée, début du chargement des scripts...');
    </script>
    
    <script src="meteoEditor.js"></script>
    <script>
        debugLog('Script meteoEditor.js chargé');
        debugLog('MeteoEditor disponible: ' + (typeof MeteoEditor !== 'undefined'));
    </script>
    
    <script src="infoPageEditor.js"></script>
    <script>
        debugLog('Script infoPageEditor.js chargé');
        debugLog('InfoPageEditor disponible: ' + (typeof InfoPageEditor !== 'undefined'));
    </script>
    
    <script>
        // Test des éditeurs
        function testMeteoEditor() {
            debugLog('Test de l\'éditeur météo...');
            
            try {
                if (typeof MeteoEditor === 'undefined') {
                    throw new Error('MeteoEditor non défini');
                }
                
                const editor = new MeteoEditor();
                debugLog('Instance MeteoEditor créée');
                
                const editorHTML = editor.createEditor();
                debugLog('HTML de l\'éditeur généré');
                
                const editorContainer = document.createElement('div');
                editorContainer.innerHTML = editorHTML;
                document.body.appendChild(editorContainer.firstElementChild);
                debugLog('Éditeur ajouté au DOM');
                
                editor.initializeEditor();
                debugLog('Éditeur initialisé');
                
                showStatus('Éditeur météo ouvert avec succès !', 'success');
                
            } catch (error) {
                debugLog('ERREUR: ' + error.message);
                showStatus('Erreur: ' + error.message, 'error');
            }
        }
        
        function testInfoEditor() {
            debugLog('Test de l\'éditeur de pages...');
            
            try {
                if (typeof InfoPageEditor === 'undefined') {
                    throw new Error('InfoPageEditor non défini');
                }
                
                const editor = new InfoPageEditor();
                debugLog('Instance InfoPageEditor créée');
                
                const editorHTML = editor.createEditor();
                debugLog('HTML de l\'éditeur généré');
                
                const editorContainer = document.createElement('div');
                editorContainer.innerHTML = editorHTML;
                document.body.appendChild(editorContainer.firstElementChild);
                debugLog('Éditeur ajouté au DOM');
                
                editor.initializeEditor();
                debugLog('Éditeur initialisé');
                
                showStatus('Éditeur de pages ouvert avec succès !', 'success');
                
            } catch (error) {
                debugLog('ERREUR: ' + error.message);
                showStatus('Erreur: ' + error.message, 'error');
            }
        }
        
        // Attacher les événements après le chargement complet
        document.addEventListener('DOMContentLoaded', function() {
            debugLog('DOM chargé, attachement des événements...');
            
            const meteoBtn = document.getElementById('test-meteo-btn');
            const infoBtn = document.getElementById('test-info-btn');
            
            if (meteoBtn) {
                meteoBtn.addEventListener('click', testMeteoEditor);
                debugLog('Événement météo attaché');
            } else {
                debugLog('ERREUR: Bouton météo non trouvé');
            }
            
            if (infoBtn) {
                infoBtn.addEventListener('click', testInfoEditor);
                debugLog('Événement info attaché');
            } else {
                debugLog('ERREUR: Bouton info non trouvé');
            }
            
            // Vérification finale
            setTimeout(() => {
                debugLog('=== VÉRIFICATION FINALE ===');
                debugLog('MeteoEditor: ' + (typeof MeteoEditor !== 'undefined' ? 'OK' : 'MANQUANT'));
                debugLog('InfoPageEditor: ' + (typeof InfoPageEditor !== 'undefined' ? 'OK' : 'MANQUANT'));
                debugLog('Boutons: ' + (meteoBtn && infoBtn ? 'OK' : 'MANQUANT'));
                
                if (typeof MeteoEditor !== 'undefined' && typeof InfoPageEditor !== 'undefined') {
                    showStatus('Tous les éditeurs sont prêts !', 'success');
                } else {
                    showStatus('Certains éditeurs ne sont pas disponibles', 'error');
                }
            }, 1000);
        });
        
        // Gestion des erreurs globales
        window.addEventListener('error', function(e) {
            debugLog('ERREUR GLOBALE: ' + e.message + ' dans ' + e.filename + ':' + e.lineno);
            showStatus('Erreur JavaScript: ' + e.message, 'error');
        });
    </script>
</body>
</html>
