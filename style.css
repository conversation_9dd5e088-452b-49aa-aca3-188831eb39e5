

@import url('https://fonts.googleapis.com/css2?family=Josefin+Sans:ital,wght@0,100..700;1,100..700&display=swap');

body, html {
    margin: 0;
    padding: 0;
    /* font-family: "Josef<PERSON> Sans", sans-serif; */
    /* font-family: 'Hind', sans-serif; */
    font-family: 'Pilcrow Rounded', sans-serif;
    background-color: #f0f8ff;
    height: 100%;
    overflow: hidden;
    color: #333;
}

:root {
    --primary-color: #346D83;
    --secondary-color: #D9E4E3;
    --tertiary-color: #7DACB7;
    --button-colorActive: #B47A58; 
    --button-colorInactive : #DBC2A3; 
    --button-color : #71808B;
  }

  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    background-color: #f0f0f0;
    border-radius: 10px;
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  }
  
  ::-webkit-scrollbar-thumb {
    background-color: var(--button-color);
    border-radius: 10px;
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background-color: #888;
  }
  
  ::-webkit-scrollbar-track {
    background-color: #f0f0f0;
    border-radius: 10px;
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  }
  
  ::-webkit-scrollbar-button {
    background-color: #f0f0f0;
    border-radius: 10px;
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  }
  
  ::-webkit-scrollbar-corner {
    background-color: #f0f0f0;
    border-radius: 10px;
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  }
      

  .material-icons {
    font-size: 24px; /* Ajustez cette valeur selon vos besoins */
    line-height: 1; /* Réduit l'espace vertical autour de l'icône */
    vertical-align:middle; /* Assure un bon alignement vertical */
    padding: 0%;
}

/* Pour les écrans de petite taille */
@media (max-width: 768px) {
  #playlist-popup {
    width: 90%; /* ajustement de la largeur pour les écrans de petite taille */
  }
}
  
  /* Pour les écrans de taille moyenne */
  @media (min-width: 769px) and (max-width: 1024px) {
    #playlist-popup {
      width: 80%;
    }
  }
  
  /* Pour les écrans de grande taille */
  @media (min-width: 1600px) {
    #playlist-popup {
      width: 50%;
    }
  }


#standby-image {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
}

#main-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    position: relative;
    background: linear-gradient(135deg, #e6f2ff 0%, #f0f8ff 100%);
}

#stream-container {
    flex-grow: 1;
    position: relative;
    overflow: hidden;
}


.stream-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

#standby-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 1;
}

#control-area {
    position: fixed; 
    bottom: -80px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(197, 219, 231, 0.438);
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    transition: bottom 0.3s ease-in-out;
    /* box-shadow: 0 -5px 15px rgba(82, 80, 80, 0.651); */
    border-radius: 20px 20px 0 0;
    backdrop-filter: blur(10px);
    width: fit-content;
    max-width: 1000px;
}

#control-area::before {
    content: '';
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    width: fit-content;
    height: 30px;
    background-color: rgba(69, 73, 77, 0.0.205);
    /* box-shadow: 0 -5px 15px rgba(0, 0, 0, 0.1); */
    border-radius: 30px 30px 0 0;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #ffffff;
    backdrop-filter: blur(10px);
    font-size: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
}

#control-area:hover::before {
    top: -35px;
    height: 35px;
}

#control-area-trigger {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 120px;
    z-index: 999;
}

#control-area-trigger:hover + #control-area,
#control-area:hover {
    bottom: 0;
}

.control-button, .nav-button, #fullscreen-button {
    display: flex;
    align-items: center; /* Aligne les éléments verticalement */
    justify-content: center; /* Centre les éléments horizontalement */
    background-color: var(--button-color);
    color: #ffffff;
    border: none;
    padding: 12px 24px;
    margin: 0 10px;
    cursor: pointer;
    font-size: 14px;
    border-radius: 30px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    font-family: "Josefin Sans", sans-serif;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
}

.control-button:hover, .nav-button:hover, #fullscreen-button:hover {
    background-color: #2a6285;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

#navigation-controls {
    display: flex;
}


#control-button :hover{
    background-color: #2a8558;}

#reset-stream:hover {
    background-color: #dd9e15;
    transform: translateY(-2px);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

h2, h3 {
  color: #0077be;
  margin-bottom: 10px;
}


#fullscreen-button:hover {
    background-color: #005c8f;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

#timer {
    position: absolute;
    bottom:  0% ;
    right: 0px;
    background-color: rgba(145, 145, 145, 0.13);
    color: rgba(107, 107, 107, 0.459);
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 14px;
    z-index: 1000;
}

#info-panel {
    position: absolute;
    top: 2%;
    right: 10px;
    width: 245px;
    height: auto;
    background-color: rgba(207, 207, 207, 0.781);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    z-index: 1000;
    display: none;
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
}

#info-panel.active {
    display: block;
}

#info-panel h3 {
    margin-top: 0;
    color: #01324e;
    font-size: 1.1em;
    border-bottom: 2px solid #01324e;
    padding-bottom: 5px;
}

#info-panel p {
    margin: 10px 0;
    font-size: 1.1em;
}


#toggle-info-panel:hover {
    background-color: #45a049;
    /* transform: translateY(-2px); */
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

#toggle-info-panel.active {
    background-color: var(--button-color);
}

#toggle-info-panel.active:hover {
    background-color: #d32f2f;
}

#tide-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 5px 10px;
    margin-top: 15px;
}

#tide-table th, #tide-table td {
    text-align: left;
    padding: 10px;
    background-color: rgba(0, 119, 190, 0.1);
    border-radius: 5px;
}

#tide-table th {
    background-color: #0077be;
    color: white;
    font-weight: bold;
}

#tide-table tr:nth-child(even) td {
    background-color: rgba(0, 162, 255, 0.301);
}

.tide-time {
    font-weight: bold;
    color: #0077be;
}

.tide-height {
    color: #333;
}

#marees-container {
    margin-top: 20px;
} 

#marees-container table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

#marees-container th, #marees-container td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

#marees-container th {
    background-color: #f2f2f2;
}

.carousel {
    position: relative;
    width: 100%;
    height: 100%;
    perspective: 1000px;
}

.slide {
    display: none;
    width: 100%;
    height: 100%;
    /* transition: opacity 0.5s ease-in-out; */
    backface-visibility: hidden;
    transform-style: preserve-3d;
    transition: transform 1s ease-in-out, opacity 1s ease-in-out;
    opacity: 0;
}

.slide.active {
    opacity: 1;
    display: block;
}

/* ==========================================================================
   13. Composants Météo - Design Minimaliste
   ========================================================================== */

#devref {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: x-small!important;
  text-align: center!important;
  margin-top: 0px;
}

.logo-menu {
  display: block;
  margin: 0 auto;
  width: 100px;
  height: 100px;
  position: relative;
  bottom: 0;
}

  #date-time {
    font-size: 2em !important;
    font-weight: bold;
    color: #535353;
    margin-bottom: 10px;
    text-align: center;
  }

  #time {
    font-size: 1.2em !important;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
    text-align: center;
  } 

#weather-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: 10px;
    border-radius: 10px;
    /* box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); */
  }
  
  #weather-description {
    font-size: 1.2em !important; 
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
  }
  
  #weather-icon {
    width: 80px;
    height: 80px;
  }
  
  #weather-temperature {
    font-size: 1.2em  !important;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
  }
  
  #weather-wind, #weather-humidity, #weather-visibility {
    font-size: 1em;
    color: #555a72;
    margin-bottom: 10px;
    white-space: pre-line; /* Permet les retours à la ligne dans le texte */
  }
  
  #weather-wind {
    font-weight: bold;
  }
  
  #weather-info p {
    margin: 0;
    padding: 0;
    border: none;
    outline: none;
    background: none;
    text-align: center;
  }

  /* Section soleil */
#sun-section {
  margin-top: 10px;
}

.sun-times {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  font-size: 0.9rem;
  color: #666;
}

.sun-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.sun-item span {
  color: #333;
  font-weight: 500;
}
  
  @media (max-width: 768px) {
    #weather-info {
      padding: 10px;
    }
    #weather-icon {
      width: 50px;
      height: 50px;
    }
    #weather-temperature {
      font-size: 24px;
    }
    #weather-wind, #weather-humidity, #weather-visibility {
      font-size: 14px;
    }
  }

  .move-button,.delete-button,.duplicate-button {
  display: flex;
  margin: 5px;
}
  .move-button {
    background-color: #4CAF50;
    color: #fff;
    border: none;
    padding: 5px 10px;
    font-size: 16px;
    cursor: pointer;
    border-radius: 5px;
    transition: background-color 0.3s ease;
  }
  
  .move-button:hover {
    background-color: #3e8e41;
  }
  
  .delete-button {
    background-color: #b80c54;
    color: #fff;
    border: none;
    padding: 5px 10px;
    font-size: 16px;
    cursor: pointer;
    border-radius: 5px;
    transition: background-color 0.3s ease;
  }
  
  .delete-button:hover {
    background-color: #e91e63;
  }
  
  .duplicate-button {
    background-color: #03A9F4;
    color: #fff;
    border: none;
    padding: 5px 10px;
    font-size: 16px;
    cursor: pointer;
    border-radius: 5px;
    transition: background-color 0.3s ease;
  }
  
  .duplicate-button:hover {
    background-color: #0288d1;
  }

  /* fin style meteo */


  /* Style pour le schedule Module */


/* Style de base pour la popup */
/* Base de la popup */
#schedule-popup {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 2000;
}

/* Contenu de la popup */
.modal-content {
  background-color: #fff;
  position: relative;
  margin: 2% auto;
  padding: 20px;
  width: 90%;
  max-width: 800px;
  border-radius: 10px;
  max-height: 80vh;
  overflow-y: auto;
}

/* Titre */
#schedule-popup h2 {
  margin: 0 0 15px 0;
  color: #346D83;
  font-size: 1.4em;
  text-align: center;
}

/* Bouton fermeture */
.close {
  position: absolute;
  right: 15px;
  top: 10px;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  border: none;
  background: none;
  padding: 5px;
}

.close:hover {
  color: #333;
}

/* Formulaire */
#schedule-form {
  margin-bottom: 15px;
}

/* Sélecteur de playlist */
#playlist-schedule-select {
  width: 100%;
  padding: 10px;
  margin-bottom: 15px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1em;
  background-color: white;
  color: #346D83;
  cursor: pointer;
}

#playlist-schedule-select:focus {
  border-color: #346D83;
  outline: none;
}

#playlist-schedule-select option {
  padding: 8px;
}

/* Inputs de temps */
.time-inputs {
  display: flex;
  gap: 15px;
  margin: 15px 0;
}

.time-inputs div {
  flex: 1;
}

.time-inputs label {
  display: block;
  margin-bottom: 5px;
  color: #555;
  font-size: 0.9em;
}

.time-inputs input[type="time"] {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1em;
}

/* Sélection des jours */
.days-selection {
  margin: 15px 0;
}

.days-selection > label {
  display: block;
  margin-bottom: 8px;
  color: #555;
  font-size: 0.9em;
}

.days-checkboxes {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #ddd;
}

.days-checkboxes label {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 5px 10px;
  background-color: white;
  border-radius: 15px;
  cursor: pointer;
  font-size: 0.9em;
  transition: background-color 0.2s;
}

.days-checkboxes label:hover {
  background-color: #e9ecef;
}

/* Bouton d'ajout */
#add-schedule {
  width: 100%;
  padding: 10px;
  background-color: #346D83;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1em;
  font-weight: 500;
  transition: background-color 0.3s;
  margin-top: 15px;
}

#add-schedule:hover {
  background-color: #2b5a6d;
}

/* Liste des planifications */
#schedules-list {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #ddd;
}

#schedules-list h3 {
  color: #346D83;
  font-size: 1.1em;
  margin-bottom: 15px;
  padding-bottom: 5px;
  border-bottom: 2px solid #eee;
}

/* Items de planification */
.schedule-item {
  background-color: #f8f9fa;
  padding: 12px 15px;
  margin-bottom: 10px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.2s;
}

.schedule-item:hover {
  transform: translateX(2px);
  border-color: #346D83;
  box-shadow: 0 2px 6px rgba(52, 109, 131, 0.1);
}

.schedule-info {
  flex: 1;
}

.schedule-info strong {
  color: #346D83;
  font-size: 1.05em;
  display: block;
  margin-bottom: 4px;
}

.schedule-time, .schedule-days {
  color: #666;
  font-size: 0.9em;
  display: block;
  margin-top: 2px;
}

/* Actions (toggle et suppression) */
.schedule-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Toggle switch */
.toggle-schedule {
  width: 46px;
  height: 24px;
  background-color: #e2e2e2;
  border-radius: 12px;
  position: relative;
  cursor: pointer;
  transition: background-color 0.3s;
}

.toggle-schedule.active {
  background-color: #4CAF50;
}

.toggle-schedule::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  background-color: white;
  border-radius: 50%;
  top: 2px;
  left: 2px;
  transition: transform 0.3s;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.toggle-schedule.active::after {
  transform: translateX(22px);
}

/* Bouton de suppression */
.btn-delete-schedule {
  background-color: #fff;
  color: #dc3545;
  border: 1px solid #dc3545;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9em;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 5px;
}

.btn-delete-schedule:hover {
  background-color: #dc3545;
  color: white;
}

.btn-delete-schedule .material-icons {
  font-size: 18px;
}

/* Responsive */
@media (max-width: 480px) {
  .modal-content {
      margin: 5% auto;
      padding: 15px;
      width: 95%;
  }

  .time-inputs {
      flex-direction: column;
  }

  .schedule-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
  }
  
  .schedule-actions {
      width: 100%;
      justify-content: flex-end;
  }
}

/* ==========================================================================
   Styles pour les Contenus Personnalisés des Éditeurs
   ========================================================================== */

/* Affichage des écrans météo personnalisés */
.meteo-display {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #2c5aa0 0%, #87ceeb 100%);
    color: white;
    padding: 20px;
    box-sizing: border-box;
    overflow-y: auto;
}

.meteo-display .meteo-header {
    text-align: center;
    margin-bottom: 30px;
    border-bottom: 2px solid rgba(255,255,255,0.3);
    padding-bottom: 20px;
}

.meteo-display .meteo-header h2 {
    margin: 0;
    font-size: 2.5em;
    font-weight: 300;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.meteo-display .update-time {
    margin-top: 10px;
    opacity: 0.8;
    font-size: 0.9em;
}

.meteo-display .meteo-grid {
    display: grid;
    gap: 20px;
    height: calc(100% - 120px);
}

.meteo-display .meteo-grid.layout-semaine {
    grid-template-columns: 2fr 1fr 1fr;
    grid-template-rows: 1fr 1fr;
}

.meteo-display .meteo-grid.layout-today {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.meteo-display .meteo-grid.layout-vent6h {
    grid-template-columns: 3fr 1fr;
}

.meteo-display .widget {
    background: rgba(255,255,255,0.15);
    border-radius: 15px;
    padding: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    transition: all 0.3s ease;
}

.meteo-display .widget:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

.meteo-display .widget-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    font-size: 1.1em;
    font-weight: 500;
}

.meteo-display .widget-content {
    text-align: center;
}

.meteo-display .widget.size-large {
    grid-column: span 2;
}

.meteo-display .widget.size-medium {
    grid-column: span 1;
}

.meteo-display .widget.size-small {
    padding: 15px;
}

/* Widgets spécifiques */
.meteo-display .widget-temperature .temp-current {
    font-size: 3em;
    font-weight: bold;
    margin-bottom: 10px;
}

.meteo-display .widget-wind .wind-speed {
    font-size: 2.5em;
    font-weight: bold;
    margin-bottom: 5px;
}

.meteo-display .widget-wind .wind-compass {
    width: 60px;
    height: 60px;
    border: 2px solid white;
    border-radius: 50%;
    margin: 15px auto;
    position: relative;
}

.meteo-display .wind-compass .compass-arrow {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2px;
    height: 25px;
    background: white;
    transform-origin: bottom center;
    transform: translate(-50%, -100%);
}

.meteo-display .week-forecast {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 10px;
}

.meteo-display .day-forecast {
    text-align: center;
    padding: 10px 5px;
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
}

.meteo-display .day-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.meteo-display .day-temp {
    font-size: 1.2em;
    margin-bottom: 3px;
}

.meteo-display .day-wind {
    font-size: 0.8em;
    opacity: 0.8;
}

/* Affichage des pages d'information personnalisées */
.info-page-display {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #333;
    padding: 30px;
    box-sizing: border-box;
    overflow-y: auto;
}

.info-page-display .page-header {
    text-align: center;
    margin-bottom: 40px;
    border-bottom: 3px solid var(--primary-color);
    padding-bottom: 20px;
}

.info-page-display .page-header h1 {
    margin: 0 0 15px 0;
    font-size: 2.5em;
    color: var(--primary-color);
    font-weight: 300;
}

.info-page-display .club-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1.1em;
    color: #666;
}

.info-page-display .page-content {
    max-width: 1000px;
    margin: 0 auto;
}

.info-page-display .content-block {
    margin-bottom: 30px;
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.info-page-display .schedule-table,
.info-page-display .info-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.info-page-display .schedule-table th,
.info-page-display .info-table th {
    background: var(--primary-color);
    color: white;
    padding: 12px;
    text-align: left;
    font-weight: 500;
}

.info-page-display .schedule-table td,
.info-page-display .info-table td {
    padding: 10px 12px;
    border-bottom: 1px solid #dee2e6;
}

.info-page-display .schedule-table tr:nth-child(even),
.info-page-display .info-table tr:nth-child(even) {
    background: #f8f9fa;
}

.info-page-display .announcement-block {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-left: 4px solid #ffc107;
    padding: 20px;
    border-radius: 8px;
}

.info-page-display .announcement-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    color: #856404;
}

.info-page-display .announcement-header h3 {
    margin: 0;
    font-size: 1.3em;
}

.info-page-display .contact-block {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-left: 4px solid #2196f3;
    padding: 20px;
    border-radius: 8px;
}

.info-page-display .contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.info-page-display .contact-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: rgba(255,255,255,0.7);
    border-radius: 6px;
}

.info-page-display .contact-item i {
    color: #2196f3;
    width: 20px;
}

.info-page-display .page-footer {
    margin-top: 40px;
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #dee2e6;
    color: #6c757d;
    font-size: 0.9em;
}

/* Responsive pour les contenus personnalisés */
@media (max-width: 768px) {
    .meteo-display .meteo-grid.layout-semaine,
    .meteo-display .meteo-grid.layout-vent6h {
        grid-template-columns: 1fr;
    }

    .meteo-display .widget.size-large {
        grid-column: span 1;
    }

    .meteo-display .week-forecast {
        grid-template-columns: repeat(3, 1fr);
        gap: 5px;
    }

    .info-page-display {
        padding: 20px;
    }

    .info-page-display .page-header h1 {
        font-size: 2em;
    }

    .info-page-display .content-block {
        padding: 20px;
    }

    .info-page-display .contact-info {
        grid-template-columns: 1fr;
    }
}
