# 🐛 Guide de Débogage Éditeurs Nautiflix - Electron

## 🚀 **Étapes de Test dans Electron**

### **1. Test Rapide avec la Page de Test**
1. **Ouvrez `testEditorsElectron.html`** dans votre application Electron
2. **Cliquez sur les boutons de test** pour vérifier que les éditeurs se chargent
3. **Consultez le log de débogage** pour voir les erreurs éventuelles

### **2. Test dans l'Application Principale**
1. **Lancez votre application Nautiflix** (`index.html`)
2. **Cherchez le bouton rouge 🐛** en haut à droite de l'écran
3. **Cliquez dessus** pour ouvrir le panneau de débogage
4. **Cliquez sur "Status"** pour voir l'état des éditeurs
5. **Utilisez "Test Météo" et "Test Info"** pour tester directement

### **3. Vérification <PERSON>**
1. **Ouvrez la console Electron** (F12 ou Ctrl+Shift+I)
2. **Tapez dans la console :**
   ```javascript
   // Vérifier les classes
   console.log('MeteoEditor:', typeof MeteoEditor);
   console.log('InfoPageEditor:', typeof InfoPageEditor);
   
   // Vérifier les boutons
   console.log('Bouton météo:', document.getElementById('open-meteo-editor-main'));
   console.log('Bouton info:', document.getElementById('open-info-editor-main'));
   ```

## 🔧 **Solutions aux Problèmes Courants**

### **Problème 1: "MeteoEditor is not defined"**
**Cause :** Le script `meteoEditor.js` ne se charge pas correctement

**Solutions :**
1. Vérifiez que le fichier `meteoEditor.js` existe dans le même dossier que `index.html`
2. Ouvrez la console et regardez s'il y a des erreurs de chargement
3. Vérifiez les permissions de fichier dans Electron

### **Problème 2: "Boutons non trouvés"**
**Cause :** Les boutons des éditeurs ne sont pas générés dans la popup

**Solutions :**
1. **Ouvrez la popup de playlist** en cliquant sur l'icône liste
2. **Vérifiez que la section "Éditeurs Intégrés" apparaît**
3. Si elle n'apparaît pas, vérifiez le HTML dans `index.html` lignes 114-130

### **Problème 3: "Styles manquants"**
**Cause :** Le fichier CSS des éditeurs ne se charge pas

**Solutions :**
1. Vérifiez que `editorsStyles.css` existe
2. Vérifiez le lien CSS dans `index.html` ligne 6
3. Rechargez l'application Electron

### **Problème 4: "Erreur lors de l'ouverture"**
**Cause :** Erreur JavaScript dans les éditeurs

**Solutions :**
1. Ouvrez la console pour voir l'erreur exacte
2. Utilisez le débogueur intégré (bouton 🐛)
3. Vérifiez que tous les fichiers sont présents

## 🛠️ **Commandes de Débogage Console**

Tapez ces commandes dans la console Electron pour diagnostiquer :

```javascript
// 1. Vérifier le chargement des scripts
console.log('Scripts chargés:');
console.log('- MeteoEditor:', typeof MeteoEditor !== 'undefined');
console.log('- InfoPageEditor:', typeof InfoPageEditor !== 'undefined');
console.log('- nautiflixEditors:', typeof window.nautiflixEditors !== 'undefined');

// 2. Vérifier les éléments DOM
console.log('Éléments DOM:');
console.log('- Bouton météo:', !!document.getElementById('open-meteo-editor-main'));
console.log('- Bouton info:', !!document.getElementById('open-info-editor-main'));
console.log('- Popup playlist:', !!document.getElementById('playlist-popup'));

// 3. Test direct des éditeurs
try {
    const editor = new MeteoEditor();
    console.log('✓ MeteoEditor fonctionne');
} catch (e) {
    console.error('✗ MeteoEditor erreur:', e.message);
}

try {
    const editor = new InfoPageEditor();
    console.log('✓ InfoPageEditor fonctionne');
} catch (e) {
    console.error('✗ InfoPageEditor erreur:', e.message);
}

// 4. Forcer l'ouverture d'un éditeur
if (typeof MeteoEditor !== 'undefined') {
    const editor = new MeteoEditor();
    const html = editor.createEditor();
    const div = document.createElement('div');
    div.innerHTML = html;
    document.body.appendChild(div.firstElementChild);
    editor.initializeEditor();
    console.log('Éditeur météo forcé ouvert');
}
```

## 📋 **Checklist de Vérification**

### **Fichiers Requis :**
- [ ] `meteoEditor.js` - Éditeur météo
- [ ] `infoPageEditor.js` - Éditeur de pages
- [ ] `editorsIntegration.js` - Intégration
- [ ] `editorsStyles.css` - Styles
- [ ] `debugEditors.js` - Débogage

### **Modifications dans index.html :**
- [ ] Lien CSS `editorsStyles.css` ajouté
- [ ] Scripts JS ajoutés avant `</body>`
- [ ] Section "Éditeurs Intégrés" dans la popup playlist
- [ ] Fonction `playItem()` modifiée pour nouveaux types
- [ ] Événements des boutons ajoutés

### **Test Fonctionnel :**
- [ ] Bouton 🐛 de débogage visible
- [ ] Popup playlist s'ouvre
- [ ] Section "Éditeurs Intégrés" visible
- [ ] Boutons "Créer Écran Météo" et "Créer Page Info" présents
- [ ] Clic sur les boutons ouvre les éditeurs
- [ ] Éditeurs fonctionnent (drag & drop, sauvegarde)

## 🚨 **En Cas de Problème Persistant**

### **Solution de Contournement :**
Si les boutons intégrés ne marchent pas, utilisez la console :

```javascript
// Ouvrir l'éditeur météo directement
const meteoEditor = new MeteoEditor();
const html = meteoEditor.createEditor();
const div = document.createElement('div');
div.innerHTML = html;
document.body.appendChild(div.firstElementChild);
meteoEditor.initializeEditor();

// Ouvrir l'éditeur de pages directement
const infoEditor = new InfoPageEditor();
const html2 = infoEditor.createEditor();
const div2 = document.createElement('div');
div2.innerHTML = html2;
document.body.appendChild(div2.firstElementChild);
infoEditor.initializeEditor();
```

### **Réinitialisation Complète :**
```javascript
// Nettoyer le localStorage
localStorage.removeItem('meteoConfigs');
localStorage.removeItem('infoPages');

// Recharger la page
location.reload();
```

## 📞 **Support**

Si les problèmes persistent :

1. **Copiez les logs** de la console
2. **Notez la version** d'Electron utilisée
3. **Vérifiez les permissions** de fichiers
4. **Testez avec `testEditorsElectron.html`** d'abord

## 🎯 **Objectif Final**

Une fois que tout fonctionne, vous devriez pouvoir :
- ✅ Cliquer sur "Playlist" dans Nautiflix
- ✅ Voir la section "Éditeurs Intégrés"
- ✅ Créer des écrans météo personnalisés
- ✅ Créer des pages d'information
- ✅ Voir le contenu s'ajouter automatiquement à la playlist
- ✅ Lire le contenu dans le flux digital signage

**Bonne chance ! 🚢**
