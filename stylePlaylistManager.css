.playlist-manager {
    font-family: 'Pilcrow Rounded', sans-serif;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
    max-width: 800px;
    margin: auto;
}

.playlist-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    border-radius: 12px 12px 0 0;
}

.tab-container {
    display: flex;
    gap: 0.5rem;
}

.tab-button {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    background: #e9ecef;
    color: #495057;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tab-button.active {
    background: var(--primary-color);
    color: white;
}

.close-button {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    background: #e9ecef;
    color: #495057;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.playlist-content {
    padding: 1.5rem;
}

.add-item-section {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
}

.type-selector {
    display: flex;
    gap: 0.5rem;
    margin: 1rem 0;
}

.type-button {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    background: #e9ecef;
    color: #495057;
    cursor: pointer;
    transition: all 0.2s ease;
}

.type-button.active {
    background: var(--primary-color);
    color: white;
}

.add-item-form {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.input-group {
    flex: 1;
    display: flex;
    gap: 0.5rem;
}

.input-group input {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #dee2e6;
    border-radius: 6px;
}

.duration-group {
    display: flex;
    gap: 0.5rem;
}

.duration-group input {
    width: 100px;
    padding: 0.5rem;
    border: 1px solid #dee2e6;
    border-radius: 6px;
}

.playlist-items {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.playlist-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 6px;
    gap: 1rem;
    cursor: move;
}

.drag-handle {
    color: #adb5bd;
    cursor: move;
}

.item-content {
    flex: 1;
}

.item-title {
    font-weight: 500;
    color: #495057;
}

.item-details {
    font-size: 0.875rem;
    color: #6c757d;
}

.item-actions {
    display: flex;
    gap: 0.5rem;
}

.action-button {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    background: #e9ecef;
    color: #495057;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.playlist-footer {
    padding: 1rem;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    border-radius: 0 0 12px 12px;
}

.apply-button, .save-button {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    background: var(--primary-color);
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
}

.cancel-button {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    background: #e9ecef;
    color: #495057;
    cursor: pointer;
    transition: all 0.2s ease;
}

button:hover {
    filter: brightness(90%);
}