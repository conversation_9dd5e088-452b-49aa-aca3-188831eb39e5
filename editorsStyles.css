/* Styles pour les Éditeurs Nautiflix */

/* ===== STYLES COMMUNS ===== */
.meteo-editor-container,
.info-editor-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 10000;
    display: flex;
    flex-direction: column;
    font-family: 'Roboto', sans-serif;
}

.editor-header {
    background: linear-gradient(135deg, #2c5aa0 0%, #87ceeb 100%);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
}

.editor-header h2 {
    margin: 0;
    font-size: 1.5em;
    display: flex;
    align-items: center;
    gap: 10px;
}

.header-controls {
    display: flex;
    gap: 10px;
}

.btn-preview, .btn-save, .btn-close {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.btn-preview {
    background: #17a2b8;
    color: white;
}

.btn-save {
    background: #28a745;
    color: white;
}

.btn-close {
    background: rgba(255,255,255,0.2);
    color: white;
    font-size: 18px;
    padding: 8px 12px;
}

.btn-preview:hover { background: #117a8b; }
.btn-save:hover { background: #1e7e34; }
.btn-close:hover { background: rgba(255,255,255,0.3); }

.editor-content {
    flex: 1;
    display: flex;
    background: #f8f9fa;
    overflow: hidden;
}

/* ===== PANNEAUX DE CONFIGURATION ===== */
.config-panel,
.page-config-panel {
    width: 300px;
    background: white;
    border-right: 1px solid #dee2e6;
    overflow-y: auto;
    padding: 20px;
}

.config-section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
}

.config-section:last-child {
    border-bottom: none;
}

.config-section h3 {
    color: #2c5aa0;
    margin-bottom: 15px;
    font-size: 1.1em;
    display: flex;
    align-items: center;
    gap: 8px;
}

.config-group {
    margin-bottom: 15px;
}

.config-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #495057;
}

.config-select,
.config-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
}

.color-picker-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.color-input {
    display: flex;
    align-items: center;
    gap: 10px;
}

.color-input label {
    flex: 1;
    margin-bottom: 0;
}

.color-input input[type="color"] {
    width: 40px;
    height: 30px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

/* ===== PANNEAUX DE WIDGETS/BLOCS ===== */
.widgets-panel,
.blocks-panel {
    width: 250px;
    background: white;
    border-right: 1px solid #dee2e6;
    overflow-y: auto;
    padding: 20px;
}

.widgets-panel h3,
.blocks-panel h3 {
    color: #2c5aa0;
    margin-bottom: 15px;
    font-size: 1.1em;
    display: flex;
    align-items: center;
    gap: 8px;
}

.widgets-grid,
.blocks-grid {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.widget-item,
.block-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    cursor: grab;
    transition: all 0.3s ease;
}

.widget-item:hover,
.block-item:hover {
    background: #e3f2fd;
    border-color: #2196f3;
    transform: translateY(-1px);
}

.widget-item:active,
.block-item:active {
    cursor: grabbing;
}

.widget-icon,
.block-icon {
    width: 30px;
    height: 30px;
    background: #2c5aa0;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.widget-info,
.block-info {
    flex: 1;
}

.widget-name,
.block-name {
    font-weight: 500;
    color: #495057;
}

.widget-type {
    font-size: 12px;
    color: #6c757d;
    display: block;
}

/* ===== ZONES DE CONSTRUCTION ===== */
.builder-panel,
.page-builder-panel {
    flex: 1;
    background: white;
    overflow-y: auto;
    padding: 20px;
}

.builder-panel h3,
.page-builder-panel h3 {
    color: #2c5aa0;
    margin-bottom: 15px;
    font-size: 1.1em;
    display: flex;
    align-items: center;
    gap: 8px;
}

.drop-zone,
.page-drop-zone {
    min-height: 400px;
    border: 2px dashed #ced4da;
    border-radius: 8px;
    position: relative;
    transition: all 0.3s ease;
}

.drop-zone.drag-over,
.page-drop-zone.drag-over {
    border-color: #2c5aa0;
    background: rgba(44, 90, 160, 0.05);
}

.drop-zone-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #6c757d;
}

.drop-zone-content i {
    font-size: 48px;
    margin-bottom: 10px;
    color: #ced4da;
}

.builder-grid,
.page-builder {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* ===== WIDGETS DANS LE BUILDER ===== */
.builder-widget,
.page-block {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.builder-widget:hover,
.page-block:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.widget-header,
.block-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    border-radius: 8px 8px 0 0;
}

.widget-title,
.block-title {
    font-weight: 500;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 8px;
}

.widget-controls,
.block-controls {
    display: flex;
    gap: 5px;
}

.widget-config,
.widget-remove,
.block-move-up,
.block-move-down,
.block-edit,
.block-remove {
    padding: 4px 8px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.widget-config,
.block-edit {
    background: #17a2b8;
    color: white;
}

.widget-remove,
.block-remove {
    background: #dc3545;
    color: white;
}

.block-move-up,
.block-move-down {
    background: #6c757d;
    color: white;
}

.widget-config:hover,
.block-edit:hover { background: #117a8b; }
.widget-remove:hover,
.block-remove:hover { background: #bd2130; }
.block-move-up:hover,
.block-move-down:hover { background: #545b62; }

.widget-preview,
.block-content {
    padding: 15px;
}

/* ===== PRÉVISUALISATIONS ===== */
.preview-panel,
.info-preview-panel {
    position: fixed;
    top: 10%;
    right: 20px;
    width: 400px;
    max-height: 80%;
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    z-index: 10001;
    overflow: hidden;
}

.preview-header {
    background: #2c5aa0;
    color: white;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.preview-content,
.info-preview-content {
    padding: 20px;
    overflow-y: auto;
    max-height: 60vh;
}

/* ===== MODAL D'ÉDITION ===== */
.content-editor-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 10002;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 80%;
    overflow: hidden;
}

.modal-header {
    background: #2c5aa0;
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 15px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.close-modal {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
}

.btn-cancel {
    background: #6c757d;
    color: white;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.btn-cancel:hover {
    background: #545b62;
}

/* ===== ÉDITEUR DE TEXTE RICHE ===== */
.editor-toolbar {
    display: flex;
    gap: 5px;
    padding: 10px;
    background: #f8f9fa;
    border: 1px solid #ced4da;
    border-bottom: none;
    border-radius: 4px 4px 0 0;
}

.editor-btn {
    padding: 6px 10px;
    border: 1px solid #ced4da;
    background: white;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.editor-btn:hover {
    background: #e9ecef;
}

.editor-content {
    min-height: 200px;
    padding: 15px;
    border: 1px solid #ced4da;
    border-radius: 0 0 4px 4px;
    outline: none;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 1200px) {
    .config-panel,
    .page-config-panel {
        width: 250px;
    }
    
    .widgets-panel,
    .blocks-panel {
        width: 200px;
    }
    
    .preview-panel,
    .info-preview-panel {
        width: 350px;
    }
}

@media (max-width: 768px) {
    .editor-content {
        flex-direction: column;
    }
    
    .config-panel,
    .page-config-panel,
    .widgets-panel,
    .blocks-panel {
        width: 100%;
        max-height: 200px;
    }
    
    .preview-panel,
    .info-preview-panel {
        position: fixed;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        border-radius: 0;
    }
}
