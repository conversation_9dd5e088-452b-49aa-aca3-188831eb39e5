// Script de débogage pour les éditeurs Nautiflix dans Electron

class EditorsDebugger {
    constructor() {
        this.logContainer = null;
        this.createDebugInterface();
    }

    createDebugInterface() {
        // Créer un panneau de débogage flottant
        const debugPanel = document.createElement('div');
        debugPanel.id = 'editors-debug-panel';
        debugPanel.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            max-height: 400px;
            background: rgba(0,0,0,0.9);
            color: white;
            border-radius: 8px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            z-index: 99999;
            overflow-y: auto;
            display: none;
        `;

        debugPanel.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                <strong>Debug Éditeurs</strong>
                <button onclick="this.parentElement.parentElement.style.display='none'" 
                        style="background: #dc3545; color: white; border: none; border-radius: 3px; padding: 2px 6px; cursor: pointer;">×</button>
            </div>
            <div id="debug-log-content" style="max-height: 300px; overflow-y: auto;">
                <div>Initialisation du débogage...</div>
            </div>
            <div style="margin-top: 10px; display: flex; gap: 5px;">
                <button onclick="editorsDebugger.testMeteoEditor()" 
                        style="background: #007bff; color: white; border: none; border-radius: 3px; padding: 5px 8px; cursor: pointer; font-size: 10px;">Test Météo</button>
                <button onclick="editorsDebugger.testInfoEditor()" 
                        style="background: #28a745; color: white; border: none; border-radius: 3px; padding: 5px 8px; cursor: pointer; font-size: 10px;">Test Info</button>
                <button onclick="editorsDebugger.checkStatus()"
                        style="background: #ffc107; color: black; border: none; border-radius: 3px; padding: 5px 8px; cursor: pointer; font-size: 10px;">Status</button>
                <button onclick="editorsDebugger.testButtons()"
                        style="background: #6f42c1; color: white; border: none; border-radius: 3px; padding: 5px 8px; cursor: pointer; font-size: 10px;">Test Boutons</button>
            </div>
        `;

        document.body.appendChild(debugPanel);
        this.logContainer = document.getElementById('debug-log-content');

        // Ajouter un bouton pour ouvrir le debug
        const debugButton = document.createElement('button');
        debugButton.id = 'open-debug-btn';
        debugButton.innerHTML = '🐛';
        debugButton.title = 'Ouvrir le débogage des éditeurs';
        debugButton.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            width: 40px;
            height: 40px;
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            z-index: 99998;
            font-size: 16px;
        `;

        debugButton.addEventListener('click', () => {
            debugPanel.style.display = debugPanel.style.display === 'none' ? 'block' : 'none';
        });

        document.body.appendChild(debugButton);
    }

    log(message, type = 'info') {
        if (!this.logContainer) return;

        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        
        let color = '#ffffff';
        switch (type) {
            case 'error': color = '#ff6b6b'; break;
            case 'success': color = '#51cf66'; break;
            case 'warning': color = '#ffd43b'; break;
            case 'info': color = '#74c0fc'; break;
        }

        logEntry.style.color = color;
        logEntry.innerHTML = `[${timestamp}] ${message}`;
        
        this.logContainer.appendChild(logEntry);
        this.logContainer.scrollTop = this.logContainer.scrollHeight;

        // Aussi dans la console
        console.log(`[EditorsDebug] ${message}`);
    }

    checkStatus() {
        this.log('=== VÉRIFICATION DU STATUT ===', 'info');
        
        // Vérifier les classes
        this.log(`MeteoEditor: ${typeof MeteoEditor !== 'undefined' ? '✓ Disponible' : '✗ Manquant'}`, 
                 typeof MeteoEditor !== 'undefined' ? 'success' : 'error');
        
        this.log(`InfoPageEditor: ${typeof InfoPageEditor !== 'undefined' ? '✓ Disponible' : '✗ Manquant'}`, 
                 typeof InfoPageEditor !== 'undefined' ? 'success' : 'error');

        this.log(`nautiflixEditors: ${typeof window.nautiflixEditors !== 'undefined' ? '✓ Disponible' : '✗ Manquant'}`, 
                 typeof window.nautiflixEditors !== 'undefined' ? 'success' : 'error');

        // Vérifier les boutons
        const meteoBtn = document.getElementById('open-meteo-editor-main');
        const infoBtn = document.getElementById('open-info-editor-main');
        
        this.log(`Bouton Météo: ${meteoBtn ? '✓ Trouvé' : '✗ Manquant'}`, meteoBtn ? 'success' : 'error');
        this.log(`Bouton Info: ${infoBtn ? '✓ Trouvé' : '✗ Manquant'}`, infoBtn ? 'success' : 'error');

        // Vérifier les styles
        const editorsStyles = document.querySelector('link[href*="editorsStyles.css"]');
        this.log(`Styles éditeurs: ${editorsStyles ? '✓ Chargés' : '✗ Manquants'}`, editorsStyles ? 'success' : 'error');

        // Vérifier localStorage
        const meteoConfigs = localStorage.getItem('meteoConfigs');
        const infoPages = localStorage.getItem('infoPages');
        
        this.log(`Configs météo sauvées: ${meteoConfigs ? Object.keys(JSON.parse(meteoConfigs)).length : 0}`, 'info');
        this.log(`Pages info sauvées: ${infoPages ? Object.keys(JSON.parse(infoPages)).length : 0}`, 'info');
    }

    testMeteoEditor() {
        this.log('=== TEST ÉDITEUR MÉTÉO ===', 'info');
        
        try {
            if (typeof MeteoEditor === 'undefined') {
                throw new Error('MeteoEditor non disponible');
            }

            this.log('Création de l\'instance MeteoEditor...', 'info');
            const editor = new MeteoEditor();

            this.log('Génération du HTML...', 'info');
            const editorHTML = editor.createEditor();

            this.log('Ajout au DOM...', 'info');
            const editorContainer = document.createElement('div');
            editorContainer.innerHTML = editorHTML;
            document.body.appendChild(editorContainer.firstElementChild);

            this.log('Initialisation...', 'info');
            editor.initializeEditor();

            this.log('✓ Éditeur météo ouvert avec succès !', 'success');

        } catch (error) {
            this.log(`✗ Erreur: ${error.message}`, 'error');
            this.log(`Stack: ${error.stack}`, 'error');
        }
    }

    testInfoEditor() {
        this.log('=== TEST ÉDITEUR PAGES ===', 'info');
        
        try {
            if (typeof InfoPageEditor === 'undefined') {
                throw new Error('InfoPageEditor non disponible');
            }

            this.log('Création de l\'instance InfoPageEditor...', 'info');
            const editor = new InfoPageEditor();

            this.log('Génération du HTML...', 'info');
            const editorHTML = editor.createEditor();

            this.log('Ajout au DOM...', 'info');
            const editorContainer = document.createElement('div');
            editorContainer.innerHTML = editorHTML;
            document.body.appendChild(editorContainer.firstElementChild);

            this.log('Initialisation...', 'info');
            editor.initializeEditor();

            this.log('✓ Éditeur de pages ouvert avec succès !', 'success');

        } catch (error) {
            this.log(`✗ Erreur: ${error.message}`, 'error');
            this.log(`Stack: ${error.stack}`, 'error');
        }
    }

    monitorScriptLoading() {
        this.log('Surveillance du chargement des scripts...', 'info');
        
        // Surveiller le chargement des scripts
        const scripts = ['meteoEditor.js', 'infoPageEditor.js', 'editorsIntegration.js'];
        
        scripts.forEach(scriptName => {
            const script = document.querySelector(`script[src*="${scriptName}"]`);
            if (script) {
                script.addEventListener('load', () => {
                    this.log(`✓ Script ${scriptName} chargé`, 'success');
                });
                script.addEventListener('error', () => {
                    this.log(`✗ Erreur de chargement ${scriptName}`, 'error');
                });
            } else {
                this.log(`✗ Script ${scriptName} non trouvé dans le DOM`, 'warning');
            }
        });
    }

    testButtons() {
        this.log('=== TEST DES BOUTONS ===', 'info');

        // Vérifier si la popup playlist est ouverte
        const popup = document.getElementById('playlist-popup');
        if (!popup || popup.style.display === 'none') {
            this.log('⚠️ Popup playlist fermée. Ouverture...', 'warning');
            const playlistBtn = document.getElementById('playlist-button');
            if (playlistBtn) {
                playlistBtn.click();
                setTimeout(() => this.testButtonsAfterPopup(), 500);
                return;
            } else {
                this.log('✗ Bouton playlist non trouvé', 'error');
                return;
            }
        }

        this.testButtonsAfterPopup();
    }

    testButtonsAfterPopup() {
        const meteoBtn = document.getElementById('open-meteo-editor-main');
        const infoBtn = document.getElementById('open-info-editor-main');

        this.log(`Bouton météo trouvé: ${meteoBtn ? '✓' : '✗'}`, meteoBtn ? 'success' : 'error');
        this.log(`Bouton info trouvé: ${infoBtn ? '✓' : '✗'}`, infoBtn ? 'success' : 'error');

        if (meteoBtn) {
            this.log('Test du clic sur le bouton météo...', 'info');
            try {
                meteoBtn.click();
                this.log('✓ Clic météo exécuté', 'success');
            } catch (error) {
                this.log(`✗ Erreur clic météo: ${error.message}`, 'error');
            }
        }

        setTimeout(() => {
            if (infoBtn) {
                this.log('Test du clic sur le bouton info...', 'info');
                try {
                    infoBtn.click();
                    this.log('✓ Clic info exécuté', 'success');
                } catch (error) {
                    this.log(`✗ Erreur clic info: ${error.message}`, 'error');
                }
            }
        }, 1000);
    }

    simulateButtonClick() {
        this.log('=== SIMULATION CLIC BOUTON ===', 'info');
        this.testButtons();
    }
}

// Initialiser le débogueur
let editorsDebugger;

// Attendre que le DOM soit prêt
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        editorsDebugger = new EditorsDebugger();
        editorsDebugger.log('Débogueur des éditeurs initialisé', 'success');
        editorsDebugger.monitorScriptLoading();
        
        // Vérification automatique après 3 secondes
        setTimeout(() => {
            editorsDebugger.checkStatus();
        }, 3000);
    });
} else {
    editorsDebugger = new EditorsDebugger();
    editorsDebugger.log('Débogueur des éditeurs initialisé', 'success');
    editorsDebugger.monitorScriptLoading();
    
    setTimeout(() => {
        editorsDebugger.checkStatus();
    }, 3000);
}

// Export global pour accès depuis la console
window.editorsDebugger = editorsDebugger;
