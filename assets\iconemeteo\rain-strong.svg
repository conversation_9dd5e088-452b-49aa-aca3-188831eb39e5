<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 64 64">
    <defs>
        <!-- Dégradé pour le nuage -->
        <linearGradient id="b" x1="22.56" x2="39.2" y1="21.96" y2="50.8" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#3f4758"/>
            <stop offset=".45" stop-color="#3f4758"/>
            <stop offset="1" stop-color="#2d3340"/>
        </linearGradient>
        <!-- Dégradé pour les gouttes de pluie -->
        <linearGradient id="a" x1="22.53" x2="25.47" y1="42.95" y2="48.05" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#4286ee"/>
            <stop offset=".45" stop-color="#4286ee"/>
            <stop offset="1" stop-color="#0950bc"/>
        </linearGradient>
        <!-- Références du même dégradé pour les autres gouttes -->
        <linearGradient id="c" x1="29.53" x2="32.47" y1="42.95" y2="48.05" xlink:href="#a"/>
        <linearGradient id="d" x1="36.53" x2="39.47" y1="42.95" y2="48.05" xlink:href="#a"/>
        <linearGradient id="e" x1="43.53" x2="46.47" y1="42.95" y2="48.05" xlink:href="#a"/>
        <linearGradient id="f" x1="22.53" x2="25.47" y1="39.95" y2="45.05" xlink:href="#a"/>
        <linearGradient id="g" x1="29.53" x2="32.47" y1="39.95" y2="45.05" xlink:href="#a"/>
        <linearGradient id="h" x1="36.53" x2="39.47" y1="39.95" y2="45.05" xlink:href="#a"/>
        <linearGradient id="i" x1="43.53" x2="46.47" y1="39.95" y2="45.05" xlink:href="#a"/>
    </defs>
    
    <!-- Nuage avec dégradé et effet de rondeur -->
    <path fill="url(#b)" stroke="#2d3340" stroke-miterlimit="10" stroke-width=".5" 
          d="M46.5 31.5h-.32a10.49 10.49 0 00-19.11-8 7 7 0 00-10.57 6 7.21 7.21 0 00.1 1.14A7.5 7.5 0 0018 45.5a4.19 4.19 0 00.5 0v0h28a7 7 0 000-14z"/>
    
    <!-- Première rangée de gouttes -->
    <path fill="none" stroke="url(#a)" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" 
          d="M24.39 43.03l-.78 4.94">
        <animateTransform attributeName="transform" dur="0.7s" repeatCount="indefinite" type="translate" values="1 -5; -2 10"/>
        <animate attributeName="opacity" dur="0.7s" repeatCount="indefinite" values="0;1;1;0"/>
    </path>
    
    <path fill="none" stroke="url(#c)" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" 
          d="M31.39 43.03l-.78 4.94">
        <animateTransform attributeName="transform" begin="-0.2s" dur="0.7s" repeatCount="indefinite" type="translate" values="1 -5; -2 10"/>
        <animate attributeName="opacity" begin="-0.2s" dur="0.7s" repeatCount="indefinite" values="0;1;1;0"/>
    </path>
    
    <path fill="none" stroke="url(#d)" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" 
          d="M38.39 43.03l-.78 4.94">
        <animateTransform attributeName="transform" begin="-0.4s" dur="0.7s" repeatCount="indefinite" type="translate" values="1 -5; -2 10"/>
        <animate attributeName="opacity" begin="-0.4s" dur="0.7s" repeatCount="indefinite" values="0;1;1;0"/>
    </path>

    <path fill="none" stroke="url(#e)" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" 
          d="M45.39 43.03l-.78 4.94">
        <animateTransform attributeName="transform" begin="-0.1s" dur="0.7s" repeatCount="indefinite" type="translate" values="1 -5; -2 10"/>
        <animate attributeName="opacity" begin="-0.1s" dur="0.7s" repeatCount="indefinite" values="0;1;1;0"/>
    </path>

    <!-- Deuxième rangée de gouttes (légèrement décalée) -->
    <path fill="none" stroke="url(#f)" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" 
          d="M24.39 40.03l-.78 4.94">
        <animateTransform attributeName="transform" begin="-0.5s" dur="0.7s" repeatCount="indefinite" type="translate" values="1 -5; -2 10"/>
        <animate attributeName="opacity" begin="-0.5s" dur="0.7s" repeatCount="indefinite" values="0;1;1;0"/>
    </path>

    <path fill="none" stroke="url(#g)" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" 
          d="M31.39 40.03l-.78 4.94">
        <animateTransform attributeName="transform" begin="-0.6s" dur="0.7s" repeatCount="indefinite" type="translate" values="1 -5; -2 10"/>
        <animate attributeName="opacity" begin="-0.6s" dur="0.7s" repeatCount="indefinite" values="0;1;1;0"/>
    </path>

    <path fill="none" stroke="url(#h)" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" 
          d="M38.39 40.03l-.78 4.94">
        <animateTransform attributeName="transform" begin="-0.7s" dur="0.7s" repeatCount="indefinite" type="translate" values="1 -5; -2 10"/>
        <animate attributeName="opacity" begin="-0.7s" dur="0.7s" repeatCount="indefinite" values="0;1;1;0"/>
    </path>

    <path fill="none" stroke="url(#i)" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" 
          d="M45.39 40.03l-.78 4.94">
        <animateTransform attributeName="transform" begin="-0.8s" dur="0.7s" repeatCount="indefinite" type="translate" values="1 -5; -2 10"/>
        <animate attributeName="opacity" begin="-0.8s" dur="0.7s" repeatCount="indefinite" values="0;1;1;0"/>
    </path>
</svg>