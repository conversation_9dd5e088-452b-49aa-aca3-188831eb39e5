# 🔍 Tests Systématiques des Éditeurs Nautiflix

## 📋 **Checklist de Tests à Effectuer**

### **Test 1 : Chargement des Scripts** ✅
- [ ] Ouvrir la console (F12)
- [ ] Vérifier : `typeof MeteoEditor !== 'undefined'`
- [ ] Vérifier : `typeof InfoPageEditor !== 'undefined'`
- [ ] Vérifier : `typeof window.nautiflixEditors !== 'undefined'`

### **Test 2 : Interface Popup Playlist**
- [ ] Cliquer sur le bouton "Playlist" (icône liste)
- [ ] Vérifier que la popup s'ouvre
- [ ] Chercher la section "Éditeurs Intégrés"
- [ ] Vérifier la présence des 2 boutons :
  - [ ] "Créer Écran Météo" (bleu)
  - [ ] "Créer Page Info" (rouge)

### **Test 3 : Boutons Fonctionnels**
- [ ] Cliquer sur "Créer Écran Météo"
- [ ] Vérifier qu'un éditeur s'ouvre (overlay plein écran)
- [ ] Fermer avec le bouton X
- [ ] Vérifier que l'éditeur se ferme complètement
- [ ] Répéter avec "Créer Page Info"

### **Test 4 : Fermeture avec Échap**
- [ ] Ouvrir un éditeur
- [ ] Appuyer sur la touche Échap
- [ ] Vérifier que l'éditeur se ferme

### **Test 5 : Instances Multiples**
- [ ] Ouvrir l'éditeur météo
- [ ] Cliquer à nouveau sur "Créer Écran Météo"
- [ ] Vérifier qu'il n'y a qu'un seul éditeur ouvert
- [ ] Ouvrir l'éditeur de pages en même temps
- [ ] Vérifier que les deux peuvent coexister

### **Test 6 : Fonctionnalités de Base**
#### Éditeur Météo :
- [ ] Glisser-déposer des widgets fonctionne
- [ ] Bouton "Aperçu" fonctionne
- [ ] Bouton "Sauvegarder" fonctionne
- [ ] Changement de couleurs fonctionne

#### Éditeur Pages :
- [ ] Glisser-déposer des blocs fonctionne
- [ ] Édition de contenu fonctionne
- [ ] Bouton "Aperçu" fonctionne
- [ ] Bouton "Sauvegarder" fonctionne

## 🐛 **Utilisation du Débogueur Intégré**

### **Étapes de Débogage :**
1. **Ouvrir le débogueur** : Cliquer sur le bouton 🐛 en haut à droite
2. **Vérifier le statut** : Cliquer sur "Status"
3. **Tester les boutons** : Cliquer sur "Test Boutons"
4. **Tester les éditeurs** : Cliquer sur "Test Météo" et "Test Info"

### **Interprétation des Résultats :**

#### ✅ **Tout OK :**
```
MeteoEditor: ✓ Disponible
InfoPageEditor: ✓ Disponible
nautiflixEditors: ✓ Disponible
Bouton Météo: ✓ Trouvé
Bouton Info: ✓ Trouvé
Styles éditeurs: ✓ Chargés
```

#### ❌ **Problèmes Possibles :**

**Scripts non chargés :**
```
MeteoEditor: ✗ Manquant
```
→ Vérifier que `meteoEditor.js` est dans le bon dossier

**Boutons non trouvés :**
```
Bouton Météo: ✗ Manquant
```
→ Ouvrir d'abord la popup playlist

**Styles manquants :**
```
Styles éditeurs: ✗ Manquants
```
→ Vérifier que `editorsStyles.css` est chargé

## 🔧 **Commandes de Débogage Console**

### **Test Rapide :**
```javascript
// Vérification complète
console.log('=== VÉRIFICATION ÉDITEURS ===');
console.log('MeteoEditor:', typeof MeteoEditor !== 'undefined');
console.log('InfoPageEditor:', typeof InfoPageEditor !== 'undefined');
console.log('Boutons:', {
    meteo: !!document.getElementById('open-meteo-editor-main'),
    info: !!document.getElementById('open-info-editor-main')
});
```

### **Forcer l'ouverture :**
```javascript
// Ouvrir l'éditeur météo directement
if (typeof openMeteoEditorSafe === 'function') {
    openMeteoEditorSafe();
} else {
    console.error('Fonction openMeteoEditorSafe non trouvée');
}
```

### **Nettoyer les éditeurs :**
```javascript
// Fermer tous les éditeurs
document.querySelectorAll('.meteo-editor-container, .info-editor-container').forEach(editor => {
    if (editor.parentNode) {
        editor.parentNode.removeChild(editor);
    }
});
console.log('Tous les éditeurs fermés');
```

### **Test des événements :**
```javascript
// Simuler un clic sur le bouton météo
const btn = document.getElementById('open-meteo-editor-main');
if (btn) {
    console.log('Bouton trouvé, simulation du clic...');
    btn.click();
} else {
    console.log('Bouton non trouvé. Ouvrir d\'abord la popup playlist.');
}
```

## 📊 **Rapport de Test**

### **À Remplir Après Chaque Test :**

**Date :** ___________  
**Version Electron :** ___________

#### **Résultats :**
- [ ] ✅ Scripts chargés correctement
- [ ] ✅ Interface popup fonctionnelle
- [ ] ✅ Boutons cliquables
- [ ] ✅ Éditeurs s'ouvrent
- [ ] ✅ Éditeurs se ferment (X et Échap)
- [ ] ✅ Pas d'instances multiples
- [ ] ✅ Fonctionnalités de base OK

#### **Problèmes Identifiés :**
1. ________________________________
2. ________________________________
3. ________________________________

#### **Solutions Appliquées :**
1. ________________________________
2. ________________________________
3. ________________________________

## 🚨 **Procédure d'Urgence**

Si rien ne fonctionne, dans la console :

```javascript
// Réinitialisation complète
localStorage.removeItem('meteoConfigs');
localStorage.removeItem('infoPages');

// Nettoyer le DOM
document.querySelectorAll('.meteo-editor-container, .info-editor-container').forEach(el => el.remove());

// Recharger
location.reload();

// Puis retester étape par étape
```

## 📞 **Support**

**Avant de demander de l'aide :**
1. ✅ Effectuer tous les tests de cette checklist
2. ✅ Noter les erreurs exactes de la console
3. ✅ Tester avec le débogueur intégré
4. ✅ Essayer les commandes de débogage console

**Informations à fournir :**
- Version d'Electron
- Logs de la console
- Résultats du débogueur
- Étapes exactes qui ne fonctionnent pas

---

**Objectif :** Avoir tous les tests ✅ avant de passer à l'utilisation normale des éditeurs.
