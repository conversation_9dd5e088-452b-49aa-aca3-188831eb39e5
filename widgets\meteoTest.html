<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON><PERSON><PERSON></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/weather-icons/2.0.12/css/weather-icons.min.css">
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-family: "Segoe UI", system-ui, -apple-system, sans-serif;
      }

      body {
        background: linear-gradient(135deg, #1a3a54 0%, #0f2942 100%);
        max-width: calc(100vw - 250px);
        min-height: 100vh;
        margin: 0;
        padding: 20px;
        color: #e6e6e6;
        overflow-x: hidden;
      }

      h1 {
        color: #ffffff;
        font-size: 1.5rem;
        margin-bottom: 30px;
        font-weight: 500;
        letter-spacing: 0.5px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        text-align: center;
      }

      .weather-container-wrapper {
        width: 100%;
        overflow-x: auto;
        padding: 10px 0;
        scrollbar-width: none;
        -ms-overflow-style: none;
      }

      .weather-container-wrapper::-webkit-scrollbar {
        display: none;
      }

      .weather-container {
        display: flex;
        gap: 15px;
        padding: 0;
        width: max-content;
        margin: 0;
      }

      .weather-card {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px;
        padding: 20px;
        backdrop-filter: blur(10px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        animation: fadeIn 0.3s ease-in-out;
        width: 200px;
        flex-shrink: 0;
      }

      .weather-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
        background: rgba(255, 255, 255, 0.08);
      }

      .date {
        text-align: center;
        font-weight: 500;
        color: #ffffff;
        margin-bottom: 10px;
        font-size: 1rem;
      }

      .weather-icon {
        text-align: center;
        margin: 15px 0 5px 0;
      }

      .weather-icon i {
        font-size: 3em;
        color: #ffffff;
      }

      .weather-desc {
        text-align: center;
        font-size: 0.9rem;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 15px;
        min-height: 2.4em;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .temp {
        font-size: 1.2em;
        color: #ffffff;
        margin: 10px 0;
        text-align: center;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .details {
        background: rgba(255, 255, 255, 0.02);
        border-radius: 10px;
        padding: 12px;
        margin-top: 10px;
        border: 1px solid rgba(255, 255, 255, 0.05);
      }

      .sun-times {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        font-size: 0.9rem;
      }

      .sun-times i {
        font-size: 1.2em;
        margin-right: 4px;
        color: #ffd700;
      }

      .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 8px 0;
        font-size: 0.9rem;
      }

      .info-label {
        color: rgba(255, 255, 255, 0.9);
      }

      .value {
        padding: 3px 8px;
        border-radius: 4px;
        background: rgba(255, 255, 255, 0.1);
        font-size: 0.9rem;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
    </style>
  </head>
  <body>
    <h1>Météo de la semaine</h1>
    <div class="weather-container-wrapper">
      <div class="weather-container" id="weatherContainer"></div>
    </div>

    <script>
      function getWeatherInfo(code) {
        const weatherCodes = {
          0: {
            icon: "wi wi-day-sunny",
            desc: "Ciel dégagé",
          },
          1: {
            icon: "wi wi-day-cloudy",
            desc: "Principalement dégagé",
          },
          2: {
            icon: "wi wi-cloudy",
            desc: "Partiellement nuageux",
          },
          3: { icon: "wi wi-cloudy", desc: "Nuageux" },
          45: { icon: "wi wi-fog", desc: "Brouillard" },
          48: {
            icon: "wi wi-fog",
            desc: "Brouillard givrant",
          },
          51: {
            icon: "wi wi-sprinkle",
            desc: "Bruine légère",
          },
          53: {
            icon: "wi wi-sprinkle",
            desc: "Bruine modérée",
          },
          55: {
            icon: "wi wi-sprinkle",
            desc: "Bruine dense",
          },
          61: { icon: "wi wi-rain", desc: "Pluie légère" },
          63: { icon: "wi wi-rain", desc: "Pluie modérée" },
          65: { icon: "wi wi-rain", desc: "Pluie forte" },
          71: { icon: "wi wi-snow", desc: "Neige légère" },
          73: { icon: "wi wi-snow", desc: "Neige modérée" },
          75: { icon: "wi wi-snow", desc: "Neige forte" },
        };
        return (
          weatherCodes[code] || {
            icon: "wi wi-na",
            desc: "Inconnu",
          }
        );
      }

      function formatDate(dateStr) {
        const date = new Date(dateStr);
        const options = { weekday: "long", day: "numeric" };
        return new Intl.DateTimeFormat("fr-FR", options).format(date);
      }

      function formatTime(timeStr) {
        const time = new Date(timeStr);
        return time.toLocaleTimeString("fr-FR", {
          hour: "2-digit",
          minute: "2-digit",
        });
      }

      fetch(
        "https://api.open-meteo.com/v1/forecast?latitude=49&longitude=-1.6&daily=weather_code,temperature_2m_max,temperature_2m_min,sunrise,sunset,precipitation_hours,precipitation_probability_max,wind_speed_10m_max,wind_gusts_10m_max&wind_speed_unit=kn&timezone=auto"
      )
        .then((response) => response.json())
        .then((data) => {
          const container = document.getElementById("weatherContainer");

          data.daily.time.forEach((date, index) => {
            const weather = getWeatherInfo(data.daily.weather_code[index]);
            const card = document.createElement("div");
            card.className = "weather-card";

            card.innerHTML = `
                        <div class="date">${formatDate(date)}</div>
                        <div class="weather-icon">
                          <i class="${weather.icon}" title="${weather.desc}"></i>
                        </div>
                        <div class="weather-desc">${weather.desc}</div>
                        <div class="temp">
                            ${Math.round(data.daily.temperature_2m_max[index])}° / 
                            ${Math.round(data.daily.temperature_2m_min[index])}°
                        </div>
                        <div class="details">
                            <div class="sun-times">
                                <span>
                                    <i class="wi wi-sunrise"></i>
                                    ${formatTime(data.daily.sunrise[index])}
                                </span>
                                <span>
                                    <i class="wi wi-sunset"></i>
                                    ${formatTime(data.daily.sunset[index])}
                                </span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Préc.</span>
                                <span class="value">${data.daily.precipitation_probability_max[index]}%</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Vent</span>
                                <span class="value">${Math.round(data.daily.wind_speed_10m_max[index])}kn</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Raf.</span>
                                <span class="value">${Math.round(data.daily.wind_gusts_10m_max[index])}kn</span>
                            </div>
                        </div>
                    `;

            container.appendChild(card);
          });
        })
        .catch((error) => {
          console.error("Erreur lors du chargement des données:", error);
          document.getElementById("weatherContainer").innerHTML =
            '<p style="color: red; text-align: center;">Erreur lors du chargement des données météo.</p>';
        });
    </script>
  </body>
</html>