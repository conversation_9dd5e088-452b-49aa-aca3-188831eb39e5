const { contextBridge, ipcRenderer } = require('electron');

// Fonction pour remplacer le texte des éléments de version
const replaceVersionInfo = () => {
  const replaceText = (selector, text) => {
    const element = document.getElementById(selector)
    if (element) element.innerText = text
  }

  for (const type of ['chrome', 'node', 'electron']) {
    replaceText(`${type}-version`, process.versions[type])
  }
}

// Exécuter le remplacement des versions au chargement du DOM
window.addEventListener('DOMContentLoaded', replaceVersionInfo);

// Exposer l'API Electron au monde du rendu

contextBridge.exposeInMainWorld('electronAPI', {
  // Autres API existantes...
  tideAPI: {
    getTidesData: () => ipcRenderer.invoke('get-tides-data')
  },
  onMareesUpdated: (callback) => ipcRenderer.on('marees-updated', callback),
  getLocalHtmlFiles: () => ipcRenderer.invoke('get-local-html-files'),
  
  // Nouvelles fonctions pour la gestion des playlists et des planifications
  getPlaylists: () => ipcRenderer.invoke('get-playlists'),
  getSchedules: () => ipcRenderer.invoke('get-schedules'),
  addSchedule: (schedule) => ipcRenderer.invoke('add-schedule', schedule),
  removeSchedule: (scheduleId) => ipcRenderer.invoke('remove-schedule', scheduleId),
  updateSchedule: (scheduleId, updates) => ipcRenderer.invoke('update-schedule', scheduleId, updates),
  onPlaylistChange: (callback) => ipcRenderer.on('change-playlist', (_event, value) => callback(value))
});
