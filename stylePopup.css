/* ==========================================================================
   1. Base Popup Styles
   ========================================================================== */
   #playlist-popup {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, #1a2942 0%, #141e30 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    padding: 30px;
    border-radius: 20px;
    z-index: 1001;
    width: 90%;
    max-width: 1200px;
    max-height: 90vh;
    overflow-y: auto;
    color: #fff;
}

/* ==========================================================================
   2. Header & Layout
   ========================================================================== */
#playlist-popup h2 {
    color: #4a9eff;
    font-size: 24px;
    margin-bottom: 25px;
    border-bottom: 2px solid #4a9eff;
    padding-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

#playlist-popup h2 .fas {
    font-size: 20px;
}

.playlist-columns {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-top: 20px;
}

/* ==========================================================================
   3. Form Elements
   ========================================================================== */
#add-item-form, 
#playlist-management {
    background: rgba(255, 255, 255, 0.05);
    padding: 20px;
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

select, 
input[type="text"], 
input[type="number"],
input[type="file"],
textarea {
    width: 100%;
    padding: 12px;
    margin: 8px 0;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: rgb(240, 240, 240);
    font-size: 14px;
    transition: all 0.3s ease;
}

select option {
    background-color: #1a2942; /* Fond sombre assorti au popup */
    color: white; /* Texte blanc */
    padding: 12px;
}
select option:hover,
select option:focus {
    background-color: #2d7dd2; /* Couleur de surbrillance */
}

select:focus, 
input:focus,
textarea:focus {
    outline: none;
    border-color: #4a9eff;
    box-shadow: 0 0 0 2px rgba(74, 158, 255, 0.2);
}

/* ==========================================================================
   4. Button Styles
   ========================================================================== */
.button-primary {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background: linear-gradient(135deg, #4a9eff 0%, #2d7dd2 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    width: 100%;
    margin: 10px 0;
}

.button-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(74, 158, 255, 0.3);
}

.button-primary .fas {
    font-size: 14px;
}

/* Action Buttons */
.button-save {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
}

.button-delete {
    background: linear-gradient(135deg, #ff4757 0%, #ff6b81 100%);
}

.button-duplicate {
    background: linear-gradient(135deg, #a55eea 0%, #8854d0 100%);
}

.move-button,
.delete-button,
.duplicate-button,
.save-duration {
    padding: 8px;
    min-width: 45px;
    height: 36px;
    border-radius: 6px;
}


/* ==========================================================================
   5. Playlist Items
   ========================================================================== */
#playlist-items {
    list-style: none;
    padding: 0;
    margin: 0;
}

#playlist-items li {
    background: rgba(255, 255, 255, 0.08);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

#playlist-items li:hover {
    background: rgba(255, 255, 255, 0.12);
    transform: translateX(5px);
}

.playlist-item-content {
    display: flex;
    align-items: center;
    gap: 10px; /* Réduit l'espace entre les éléments */
}

.item-type {
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 500;
    flex: 1; /* Prend l'espace disponible */
}

.playlist-item-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* ==========================================================================
   6. Duration Controls
   ========================================================================== */
   .duration-container {
    display: flex;
    align-items: center;
    gap: 6px;
    margin: 8px 0;
    max-width: 200px; /* Largeur maximale réduite */
}

.duration-container label {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #fff;
    font-size: 13px;
    white-space: nowrap;
}

.duration-group {
    display: flex;
    align-items: center;
    gap: 4px;
    background: rgba(255, 255, 255, 0.05);
    padding: 2px 6px;
    border-radius: 4px;
    width: auto; /* Laisse le conteneur s'adapter à son contenu */
    min-width: fit-content;
}

.duration-group .duration-input {
    width: 45px; /* Largeur minimale pour 2-3 chiffres */
    height: 24px; /* Hauteur réduite */
    text-align: center;
    padding: 2px 4px;
    font-size: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
}


/* ==========================================================================
   7. Management Groups & Labels
   ========================================================================== */
.management-group {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.management-group:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.management-group h3 {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #4a9eff;
    margin-bottom: 15px;
    font-size: 18px;
}

#playlist-label {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(74, 158, 255, 0.1);
    padding: 12px 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    font-weight: 500;
}

/* ==========================================================================
   8. Utility Classes
   ========================================================================== */
.button-active {
    transform: scale(0.95);
    opacity: 0.8;
}

.move-buttons {
    display: flex;
    gap: 4px;
}

.fas {
    color: inherit;
}

/* Hidden Elements */
#iframe-name-input, 
#webpage-url-input {
    display: none;
}

/* ==========================================================================
   9. Close Button & Notifications
   ========================================================================== */
#close-playlist {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: white;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 20px;
}

#close-playlist:hover {
    background: rgba(255, 0, 0, 0.2);
    transform: rotate(90deg);
}

.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 24px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 1100;
    opacity: 1;
    transition: opacity 0.3s ease;
}

.notification.success {
    background: linear-gradient(135deg, #4CAF50, #45a049);
}

.notification.error {
    background: linear-gradient(135deg, #ff4757, #ff6b81);
}

.notification.info {
    background: linear-gradient(135deg, #4a9eff, #2d7dd2);
}

/* ==========================================================================
   10. Scrollbar Styles
   ========================================================================== */
#playlist-popup::-webkit-scrollbar {
    width: 8px;
}

#playlist-popup::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
}

#playlist-popup::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
}

#playlist-popup::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* ==========================================================================
   11. Responsive Styles
   ========================================================================== */
@media (max-width: 768px) {
    #playlist-popup {
        padding: 20px;
        width: 95%;
    }

    .playlist-columns {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .button-primary {
        padding: 10px 16px;
    }

    #playlist-label {
        padding: 8px 12px;
    }

    .playlist-item-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .playlist-item-controls {
        flex-wrap: wrap;
        justify-content: flex-end;
        margin-top: 10px;
    }

    .move-buttons {
        order: 2;
    }

    .duration-container {
        max-width: 160px;
    }
    
    .duration-group {
        width: auto;
    }
}


@media (min-width: 769px) and (max-width: 1024px) {
    #playlist-popup {
        width: 85%;
    }
    
    .playlist-columns {
        gap: 20px;
    }
}

@media (min-width: 1025px) {
    #playlist-popup {
        width: 80%;
    }
}