<html><head><base href="https://play.local/"><title>Nautiflix Lecteur Video</title>
<style>
  @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap');
  
  :root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --background-color: #f5f7fa;
    --text-color: #2c3e50;
    --card-bg-color: #ffffff;
  }

  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  body {
    font-family: 'Roboto', sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
  }

  .container {
    display: flex;
    flex-direction: column;
    background-color: var(--card-bg-color);
    border-radius: 12px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    padding: 30px;
    max-width: 1200px;
    width: 100%;
  }

  .video-container {
    margin-bottom: 30px;
  }

  h1, h2 {
    font-weight: 300;
    margin-bottom: 20px;
    color: var(--primary-color);
  }

  #videoPlayer {
    width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  }

  .controls, .playlist-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 20px;
    gap: 10px;
  }

  button, label {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 24px;
    font-size: 16px;
    cursor: pointer;
    border-radius: 50px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 500;
  }

  button:hover, label:hover {
    background-color: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  #fileInput, #folderInput {
    display: none;
  }

  .playlist-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 10px;
  }

  #playlist {
    list-style-type: none;
  }

  .playlist-item {
    padding: 12px;
    border-bottom: 1px solid #ecf0f1;
    cursor: pointer;
    transition: background-color 0.3s ease;
    display: flex;
    align-items: center;
  }

  .playlist-item:hover {
    background-color: #f5f7fa;
  }

  .playlist-item.active {
    background-color: #e8f5fd;
    font-weight: 500;
  }

  .playlist-item-thumbnail {
    width: 80px;
    height: 45px;
    object-fit: cover;
    margin-right: 10px;
    border-radius: 4px;
    background-color: #f0f0f0;
  }

  .playlist-item-info {
    display: flex;
    flex-direction: column;
  }

  .playlist-item-title {
    font-weight: 500;
    margin-bottom: 4px;
  }

  .playlist-item-duration {
    font-size: 12px;
    color: #7f8c8d;
  }

  /* Responsive design */
  @media (min-width: 768px) {
    .container {
      flex-direction: row;
    }

    .video-container {
      flex: 2;
      margin-right: 30px;
      margin-bottom: 0;
    }

    .playlist-container {
      flex: 1;
    }
  }
</style>
</head>
<body>
  <div class="container">
    <div class="video-container">
      <!-- <h1>Lecteur vidéo 4K</h1> -->
      <video id="videoPlayer" controls>
        Votre navigateur ne supporte pas la balise vidéo.
      </video>
      <div class="controls">
        <label for="fileInput">Ajouter des vidéos</label>
        <input type="file" id="fileInput" accept="video/*" multiple>
        <label for="folderInput">Sélectionner un dossier</label>
        <input type="file" id="folderInput" webkitdirectory directory multiple>
        <button id="playPauseBtn">Lecture/Pause</button>
      </div>
    </div>
    <div class="playlist-container">
      <h2>Playlist</h2>
      <ul id="playlist"></ul>
      <div class="playlist-controls">
        <button id="prevBtn">Précédent</button>
        <button id="nextBtn">Suivant</button>
        <button id="shuffleBtn">Mélanger</button>
      </div>
    </div>
  </div>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/hls.js/1.1.5/hls.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.14.0/Sortable.min.js"></script>
    <script>
    const videoPlayer = document.getElementById('videoPlayer');
    const fileInput = document.getElementById('fileInput');
    const folderInput = document.getElementById('folderInput');
    const playPauseBtn = document.getElementById('playPauseBtn');
    const playlist = document.getElementById('playlist');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const shuffleBtn = document.getElementById('shuffleBtn');

    let playlistItems = [];
    let currentIndex = 0;

    fileInput.addEventListener('change', (e) => {
      handleFiles(e.target.files);
    });

    folderInput.addEventListener('change', (e) => {
      handleFiles(e.target.files);
    });

    function handleFiles(files) {
      for (let file of files) {
        if (file.type.startsWith('video/')) {
          addToPlaylist(file);
        }
      }
      if (playlistItems.length === 0) {
        loadVideo(0);
      }
    }

    function addToPlaylist(file) {
      const videoURL = URL.createObjectURL(file);
      const tempVideo = document.createElement('video');
      tempVideo.src = videoURL;
      tempVideo.addEventListener('loadedmetadata', () => {
        const duration = formatDuration(tempVideo.duration);
        generateThumbnail(tempVideo).then(thumbnail => {
          playlistItems.push({ name: file.name, url: videoURL, duration, thumbnail });
          updatePlaylistUI();
        });
      });
    }

    function formatDuration(seconds) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.floor(seconds % 60);
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    function generateThumbnail(video) {
      return new Promise((resolve) => {
        const canvas = document.createElement('canvas');
        canvas.width = 160;
        canvas.height = 90;
        video.currentTime = 1; // Set to 1 second to avoid black frames
        video.addEventListener('seeked', () => {
          canvas.getContext('2d').drawImage(video, 0, 0, canvas.width, canvas.height);
          resolve(canvas.toDataURL());
        }, { once: true });
      });
    }

    function updatePlaylistUI() {
      playlist.innerHTML = '';
      playlistItems.forEach((item, index) => {
        const li = document.createElement('li');
        li.className = 'playlist-item';
        if (index === currentIndex) {
          li.classList.add('active');
        }
        li.innerHTML = `
          <img class="playlist-item-thumbnail" src="${item.thumbnail}" alt="Miniature de ${item.name}">
          <div class="playlist-item-info">
            <span class="playlist-item-title">${item.name}</span>
            <span class="playlist-item-duration">${item.duration}</span>
          </div>
        `;
        li.addEventListener('click', () => loadVideo(index));
        playlist.appendChild(li);
      });
      initDragAndDrop();
    }

    function initDragAndDrop() {
      new Sortable(playlist, {
        animation: 150,
        onEnd: function (evt) {
          const oldIndex = evt.oldIndex;
          const newIndex = evt.newIndex;
          const [movedItem] = playlistItems.splice(oldIndex, 1);
          playlistItems.splice(newIndex, 0, movedItem);
          currentIndex = playlistItems.findIndex(item => item.url === videoPlayer.src);
          updatePlaylistUI();
        }
      });
    }

    function loadVideo(index) {
      if (index >= 0 && index < playlistItems.length) {
        currentIndex = index;
        videoPlayer.src = playlistItems[index].url;
        videoPlayer.play();
        updatePlaylistUI();
      }
    }

    playPauseBtn.addEventListener('click', () => {
      if (videoPlayer.paused) {
        videoPlayer.play();
      } else {
        videoPlayer.pause();
      }
    });

    prevBtn.addEventListener('click', () => {
      loadVideo(currentIndex - 1);
    });

    nextBtn.addEventListener('click', () => {
      loadVideo(currentIndex + 1);
    });

    shuffleBtn.addEventListener('click', () => {
      for (let i = playlistItems.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [playlistItems[i], playlistItems[j]] = [playlistItems[j], playlistItems[i]];
      }
      currentIndex = 0;
      updatePlaylistUI();
      loadVideo(currentIndex);
    });

    videoPlayer.addEventListener('ended', () => {
      loadVideo(currentIndex + 1);
    });

    if (Hls.isSupported()) {
      const hls = new Hls({
        maxBufferLength: 30,
        maxMaxBufferLength: 600,
        maxBufferSize: 60 * 1000 * 1000,
      });
      hls.attachMedia(videoPlayer);
      hls.on(Hls.Events.MEDIA_ATTACHED, function () {
        console.log("HLS.js attached and ready");
      });
    }

    videoPlayer.addEventListener('error', (e) => {
      console.error('Erreur de lecture vidéo:', e);
      alert('Une erreur est survenue lors de la lecture de la vidéo. Passage à la suivante.');
      loadVideo(currentIndex + 1);
    });

    videoPlayer.addEventListener('canplay', () => {
      videoPlayer.play();
    });

    videoPlayer.addEventListener('waiting', () => {
      console.log('Mise en mémoire tampon...');
    });

    videoPlayer.addEventListener('loadedmetadata', () => {
      if (videoPlayer.videoWidth >= 3840 || videoPlayer.videoHeight >= 2160) {
        console.log('Vidéo 4K détectée');
      }
    });

        // Drag and drop for files
const dropZone = document.body;

dropZone.addEventListener('dragover', (e) => {
  e.preventDefault();
  e.stopPropagation();
  dropZone.style.backgroundColor = 'rgba(52, 152, 219, 0.2)';
});

dropZone.addEventListener('dragleave', (e) => {
  e.preventDefault();
  e.stopPropagation();
  dropZone.style.backgroundColor = '';
});

dropZone.addEventListener('drop', (e) => {
  e.preventDefault();
  e.stopPropagation();
  dropZone.style.backgroundColor = '';
  const files = e.dataTransfer.files;
  handleFiles(files);
});
  </script>
</body></html>