const backgroundImages = [
    './assets/Designer6.jpeg',
    './assets/Designer7.jpeg',
    './assets/windsurfer01.png',
    './assets/windsurfer2.png',
    // Ajoutez autant d'images que vous le souhaitez
  ];

  
  function changeBackgroundImage() {
    const standbyImage = document.getElementById('standby-image');
    const randomIndex = Math.floor(Math.random() * backgroundImages.length);
    standbyImage.style.backgroundImage = `url('${backgroundImages[randomIndex]}')`;
}

function startStream() {
    if (!isPlaying) {
        isPlaying = true;
        document.getElementById('standby-image').style.display = 'none';
        playNextItem();
    }
}

function playNextItem() {
    clearTimeout(currentTimer);
    if (currentItemIndex >= currentPlaylist.length) {
        currentItemIndex = 0;
    }
    playItem(currentPlaylist[currentItemIndex]);
    currentItemIndex++;
}

function playPreviousItem() {
    clearTimeout(currentTimer);
    currentItemIndex = (currentItemIndex - 2 + currentPlaylist.length) % currentPlaylist.length;
    playNextItem();
}

function playItem(item) {
    const currentContent = document.getElementById('stream-content-1');
    const nextContent = document.getElementById('stream-content-2');
    
    nextContent.innerHTML = '';
    
    if (item.type === 'image') {
        const img = document.createElement('img');
        img.src = item.content;
        img.alt = "Image du flux nautique";
        img.style.maxWidth = '100%';
        img.style.maxHeight = '100%';
        nextContent.appendChild(img);
        startTimer(item.duration);
    } else if (item.type === 'video') {
        const video = document.createElement('video');
        video.src = item.content;
        video.autoplay = true;
        video.muted = false; // Permettre l'audio par défaut
        video.controls = true; // Ajouter les contrôles vidéo
        video.style.maxWidth = '100%';
        video.style.maxHeight = '100%';
        video.onloadedmetadata = function() {
            startTimer(video.duration * 1000);
        };
        video.onended = playNextItem;
        nextContent.appendChild(video);
    } else if (item.type === 'webpage' ||  item.type === 'local-webpage') {
        const iframe = document.createElement('iframe');
        iframe.src = item.content;
        iframe.style.width = '100%';
        iframe.style.height = '100%';
        iframe.style.border = 'none';
        nextContent.appendChild(iframe);
        startTimer(item.duration);
    } else if (item.type === 'iframe') {
        nextContent.innerHTML = item.content;
        startTimer(item.duration);
    }
    
    currentContent.style.opacity = '0';
    nextContent.style.opacity = '1';
    
    setTimeout(() => {
        const temp = currentContent.id;
        currentContent.id = nextContent.id;
        nextContent.id = temp;
    }, 1000);
}

function startTimer(duration) {
    const timerElement = document.getElementById('timer');
    remainingTime = duration;
    
    function updateTimer() {
        const minutes = Math.floor(remainingTime / 60000);
        const seconds = ((remainingTime % 60000) / 1000).toFixed(0);
        timerElement.textContent = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
        
        if (remainingTime > 0) {
            remainingTime -= 1000;
            currentTimer = setTimeout(updateTimer, 1000);
        } else {
            playNextItem();
        }
    }
    
    updateTimer();
}


function resetStream() {
    isPlaying = false;
    currentItemIndex = 0;
    clearTimeout(currentTimer);
    const streamContent1 = document.getElementById('stream-content-1');
    const streamContent2 = document.getElementById('stream-content-2');
    
    streamContent1.innerHTML = '';
    streamContent2.innerHTML = '';
    streamContent1.style.opacity = '0';
    streamContent2.style.opacity = '0';
    
    document.getElementById('standby-image').style.display = 'block';
    document.getElementById('timer').textContent = '';
    
    showNotification('Le flux a été réinitialisé. Cliquez sur "Démarrer le flux" pour recommencer.');
}