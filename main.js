//================================================
// 1. IMPORTS ET CONSTANTES
//================================================
const { app, BrowserWindow, screen, Menu, ipcMain } = require('electron');
const path = require('node:path');
const axios = require('axios');
const fs = require('fs');
const os = require('os');
const MareaTidesManager = require('./mareaTides');
const ScheduleManager = require('./scheduleManager');

if (require('electron-squirrel-startup')) {
  app.quit();
}

//================================================
// 2. CONFIGURATION ET VARIABLES GLOBALES
//================================================
let mainWindow;
let videoPlayerWindow;
let mareaTidesManager;
let scheduleManager;


const userDataPath = app.getPath('userData');
const widgetsFolderPath = path.join(userDataPath, 'widgets');
const userDocumentsPath = path.join(os.homedir(), 'Documents');
const targetDir = path.join(userDocumentsPath, 'NautiflixMedia');

const JSONBIN_API_KEY = '$2a$10$XW9JkxUVwLrKM7kDqNacJOkSZUCJjhuU8jXK7uuVKjOKjXiJCS1qi';
const JSONBIN_BIN_ID = '66a0c248acd3cb34a86a8dee';
const mareeCNCPath = path.join(userDataPath, 'MareeCNC.json');
const originalPath = path.join(__dirname, 'MareeCNC.json');


// Vérifiez si le dossier existe déjà, sinon créez-le
if (!fs.existsSync(targetDir)) {
  fs.mkdirSync(targetDir, { recursive: true });
  console.log(`Dossier créé: ${targetDir}`);
} else {
  console.log(`Dossier déjà existant: ${targetDir}`);
}

const playlistsPath = path.join(app.getPath('userData'), 'playlists');

// Ajoutez cette fonction pour lire les playlists
function getPlaylistsFromStorage() {
  try {
    // Vérifie si le dossier playlists existe
    if (!fs.existsSync(playlistsPath)) {
      fs.mkdirSync(playlistsPath, { recursive: true });
      return [];
    }

    // Lit tous les fichiers .json dans le dossier playlists
    const files = fs.readdirSync(playlistsPath).filter(file => file.endsWith('.json'));
    const playlists = [];

    for (const file of files) {
      const filePath = path.join(playlistsPath, file);
      const content = fs.readFileSync(filePath, 'utf8');
      const playlist = JSON.parse(content);
      playlists.push({
        name: path.basename(file, '.json'),
        items: playlist
      });
    }

    return playlists;
  } catch (error) {
    console.error('Erreur lors de la lecture des playlists:', error);
    return [];
  }
}

//================================================
// 3. GESTION DES MARÉES
//================================================
function initializeMareaTidesManager() {
  mareaTidesManager = new MareaTidesManager(
    '5d867979-a8f7-496f-884f-479ab0c6f338',
    app.getPath('userData')
  );
}

function setupTidesUpdate() {
  if (!mareaTidesManager) {
    console.error('MareaTidesManager non initialisé');
    return;
  }

  // Vérification initiale
  mareaTidesManager.getTidesData(49.0464, -1.6319)
    .then(() => {
      console.log('Initialisation des données de marée terminée');
    })
    .catch(error => {
      console.error('Erreur lors de l\'initialisation des données:', error);
    });

  // Vérification tous les 5 jours pour correspondre au cache
  const checkInterval = 5 * 24 * 60 * 60 * 1000;

  setInterval(() => {
    mareaTidesManager.getTidesData(49.0464, -1.6319)
      .catch(error => {
        console.error('Erreur lors de la vérification périodique:', error);
      });
  }, checkInterval);
}

async function updateMareesCNCFromJsonBin() {
  try {
    const response = await axios.get(`https://api.jsonbin.io/v3/b/${JSONBIN_BIN_ID}/latest`, {
      headers: {
        'X-Master-Key': JSONBIN_API_KEY
      }
    });

    fs.writeFileSync(mareeCNCPath, JSON.stringify(response.data.record, null, 2));
    return { success: true };
  } catch (error) {
    console.error('Erreur lors de la mise à jour:', error);
    return { success: false, error: error.message };
  }
}

function initializeScheduleManager() {
  scheduleManager = new ScheduleManager();
  scheduleManager.setPlaylistChangeCallback((playlistName) => {
    if (mainWindow) {
      mainWindow.webContents.send('change-playlist', playlistName);
    }
  });
  scheduleManager.start();
}

//================================================
// 4. GESTION DES FICHIERS ET DOSSIERS
//================================================
function checkAndCopyMareeCNCJson() {
  if (!fs.existsSync(mareeCNCPath)) {
    try {
      fs.copyFileSync(originalPath, mareeCNCPath);
      console.log('MareeCNC.json copié avec succès');
    } catch (err) {
      console.error('Erreur lors de la copie de MareeCNC.json:', err);
    }
  }
}

function ensureWidgetsFolderExists() {
  if (!fs.existsSync(widgetsFolderPath)) {
    fs.mkdirSync(widgetsFolderPath, { recursive: true });
    console.log(`Dossier créé: ${widgetsFolderPath}`);
  } else {
    console.log(`Dossier déjà existant: ${widgetsFolderPath}`);
  }
}

function copyWidgetsToNautiflixMedia() {
  const targetDir = path.join(os.homedir(), 'Documents', 'NautiflixMedia');
  
  if (!fs.existsSync(targetDir)) {
    fs.mkdirSync(targetDir, { recursive: true });
  }

  function copyRecursiveSync(src, dest) {
    const exists = fs.existsSync(src);
    const stats = exists && fs.statSync(src);
    const isDirectory = exists && stats.isDirectory();

    if (isDirectory) {
      if (!fs.existsSync(dest)) {
        fs.mkdirSync(dest);
      }
      fs.readdirSync(src).forEach(childItemName => {
        copyRecursiveSync(path.join(src, childItemName), path.join(dest, childItemName));
      });
    } else {
      fs.copyFileSync(src, dest);
    }
  }

  copyRecursiveSync(widgetsFolderPath, path.join(targetDir, 'widgets'));
}

function getLocalHtmlFiles() {
  try {
    const files = fs.readdirSync(widgetsFolderPath);
    return files.filter(file => path.extname(file).toLowerCase() === '.html');
  } catch (error) {
    console.error('Erreur lors de la lecture du dossier widgets:', error);
    return [];
  }
}

//================================================
// 5. CRÉATION DES FENÊTRES
//================================================
function createWindow() {
  // Configuration de base de l'application
  if (process.platform === 'win32') {
    app.setAppUserModelId('com.nautiflix.app');
    app.setName('');
  }

  mainWindow = new BrowserWindow({
    width: 1280,
    height: 900,
    icon: path.join(__dirname, 'Assets', 'icon.ico'),
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: false,
    }
  });

  // Configuration avant le chargement
  mainWindow.setTitle('Nautiflix - CNC');
  mainWindow.maximize();

  // Créer et appliquer le menu
  const mainMenuTemplate = getMainMenuTemplate();
  const menu = Menu.buildFromTemplate(mainMenuTemplate);
  
  // Appliquer le menu de manière native
  mainWindow.setMenu(null); // Nettoyer d'abord
  Menu.setApplicationMenu(menu); // Appliquer le menu global

  // Charger le contenu
  mainWindow.loadFile('index.html');

  // Gestionnaires d'événements
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Empêcher les changements de titre
  mainWindow.on('page-title-updated', (event) => {
    event.preventDefault();
  });

  // Maintenir le menu même en plein écran
  mainWindow.on('enter-full-screen', () => {
    mainWindow.setMenuBarVisibility(false);
  });
  mainWindow.on('leave-full-screen', () => {
    mainWindow.setMenuBarVisibility(true);
  });

  // Gestionnaire de fermeture
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

function createVideoPlayerWindow() {
  videoPlayerWindow = new BrowserWindow({
    width: 1200,
    height: 720,
    parent: mainWindow,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'videoPreload.js')
    }
  });
  videoPlayerWindow.setMenu(null);
  videoPlayerWindow.loadFile('videoPlayer.html');

  videoPlayerWindow.on('closed', () => {
    videoPlayerWindow = null;
  });
}

function createAboutWindow() {
  const aboutWindow = new BrowserWindow({
    width: 800,
    height: 800,
    parent: mainWindow,
    modal: true,
    webPreferences: {
      nodeIntegration: true
    }
  });
  aboutWindow.setMenu(null);
  aboutWindow.loadFile('About.html');
}

function createDocumentationWindow() {
  const aboutWindow = new BrowserWindow({
    width: 800,
    height: 800,
    parent: mainWindow,
    modal: true,
    webPreferences: {
      nodeIntegration: true
    }
  });
  aboutWindow.setMenu(null);
  aboutWindow.loadFile('Documentation.html');
}

let playlistWindow = null;

function createPlaylistWindow() {
  if (playlistWindow) {
    playlistWindow.focus();
    return;
  }

  playlistWindow = new BrowserWindow({
    width: 800,
    height: 700,
    minWidth: 600,
    minHeight: 400,
    modal: true, // Rend la fenêtre modale
    parent: mainWindow, // Définit la fenêtre principale comme parent
    frame: false, // Supprime le cadre de fenêtre par défaut
    resizable: true,
    maximizable: false, // Empêche la maximisation
    minimizable: false, // Empêche la minimisation
    center: true, // Centre la fenêtre par rapport au parent
    backgroundColor: '#f5f6fa',
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'playlistPreload.js')
    }
  });

  // Chargement du contenu
  playlistWindow.loadFile('playlist.html');

  // Empêcher la fermeture accidentelle
  playlistWindow.on('close', (e) => {
    if (playlistWindow) {
      e.preventDefault();
      playlistWindow.hide();
    }
  });

  // Nettoyage lors de la fermeture de l'application
  mainWindow.on('closed', () => {
    if (playlistWindow) {
      playlistWindow.destroy();
      playlistWindow = null;
    }
  });

  // Gérer l'état de focus
  playlistWindow.on('blur', () => {
    if (!mainWindow.isFocused()) {
      playlistWindow.focus();
    }
  });
}


//================================================
// 6. GESTION DES IPC
//================================================
function setupIpcHandlers() {
  ipcMain.handle('get-icon-path', (event, iconName) => {
    return path.join(app.getAppPath(), 'assets', 'iconemeteo', iconName);
  });

  ipcMain.handle('get-tides-data', async (event) => {
    try {
      if (!mareaTidesManager) {
        throw new Error('MareaTidesManager non initialisé');
      }
      const data = await mareaTidesManager.getTidesData(49.0464, -1.6319);
      return mareaTidesManager.formatTideData(data);
    } catch (error) {
      console.error('Erreur lors de la récupération des données de marée:', error);
      throw error;
    }
  });

  ipcMain.handle('get-local-html-files', async (event) => {
    try {
      const files = fs.readdirSync(widgetsFolderPath);
      return files.filter(file => path.extname(file).toLowerCase() === '.html');
    } catch (error) {
      console.error('Erreur lors de la lecture du dossier widgets:', error);
      return [];
    }
  });

  ipcMain.handle('get-widget-data', async () => {
    try {
      const mareesData = await fs.promises.readFile(mareeCNCPath, 'utf8');
      return {
        marees: JSON.parse(mareesData)
      };
    } catch (error) {
      console.error('Erreur lors de la lecture des données widget:', error);
      return { marees: [] };
    }
  });
}

// Ajout des handlers IPC pour la gestion de la fenêtre playlist
ipcMain.handle('close-playlist-window', () => {
  if (playlistWindow) {
    playlistWindow.hide();
  }
});

ipcMain.handle('minimize-playlist-window', () => {
  if (playlistWindow) {
    playlistWindow.minimize();
  }
});

ipcMain.handle('get-playlists', async (event) => {
  try {
    // On demande au renderer de nous envoyer les playlists
    const playlists = await event.sender.executeJavaScript(`
      Object.keys(JSON.parse(localStorage.getItem('savedPlaylists') || '{}'))
    `);
    
    console.log('Playlists trouvées:', playlists);
    return playlists.map(name => ({ name }));
  } catch (error) {
    console.error('Erreur lors de la lecture des playlists:', error);
    return [];
  }
});

ipcMain.handle('load-playlist', async (event, playlistName) => {
  try {
    const playlist = await event.sender.executeJavaScript(`
      const playlists = JSON.parse(localStorage.getItem('savedPlaylists') || '{}');
      playlists['${playlistName}'];
    `);
    return playlist;
  } catch (error) {
    console.error('Erreur lors du chargement de la playlist:', error);
    return null;
  }
});

ipcMain.handle('get-schedules', () => {
  return scheduleManager.getSchedules();
});

ipcMain.handle('add-schedule', (event, schedule) => {
  scheduleManager.addSchedule(schedule);
});

ipcMain.handle('remove-schedule', (event, scheduleId) => {
  scheduleManager.removeSchedule(scheduleId);
});

ipcMain.handle('update-schedule', (event, scheduleId, updates) => {
  scheduleManager.updateSchedule(scheduleId, updates);
});


//================================================
// 7. MENU PRINCIPAL
//================================================
function getMainMenuTemplate() {
  return [
    {
      label: 'Fichier',
      submenu: [
        {
          label: 'Quitter',
          accelerator: 'Ctrl+Q',
          click() {
            app.quit();
          }
        },
        { 
          label: 'Recharger', 
          accelerator: 'CmdOrCtrl+R', 
          click: () => mainWindow.reload() 
        },
        {
          label: 'Options',
          submenu: [
            {
              label: 'Mettre à jour les marées',
              click: async () => {
                const result = await updateMareesCNCFromJsonBin();
                if (result.success) {
                  console.log('Données des marées mises à jour avec succès');
                  mainWindow.webContents.send('marees-updated');
                } else {
                  console.error('Erreur lors de la mise à jour des données des marées:', result.error);
                }
              }
            }
          ]
        },
      ]
    },
    {
      label: 'Vue',
      submenu: [
        {
          label: 'Toggle Developer Tools',
          accelerator: 'Ctrl+Shift+I',
          click() {
            mainWindow.webContents.toggleDevTools();
          }
        }
      ]
    },
    {
      label: 'Videos',
      submenu: [
        {
          label: 'Ouvrir le lecteur vidéo',
          click() {
            if (videoPlayerWindow) {
              videoPlayerWindow.focus();
            } else {
              createVideoPlayerWindow();
            }
          }
        }
      ]
    },
    {
      label: 'Aide',
      submenu: [
        {
          label: 'Documentation',
          click() {
            createDocumentationWindow();
          }
        },
        {
          label: 'A propos',
          click() {
            createAboutWindow();
          }
        }
      ]
    }
  ];
}

//================================================
// 8. INITIALISATION ET ÉVÉNEMENTS
//================================================
function initialize() {
  checkAndCopyMareeCNCJson();
  ensureWidgetsFolderExists();
  copyWidgetsToNautiflixMedia();
  initializeMareaTidesManager();
  setupIpcHandlers();
  initializeScheduleManager(); 
  createWindow();
  setupTidesUpdate();
}

app.on('ready', initialize);

app.on('window-all-closed', () => {
  scheduleManager.stop();
  if (process.platform !== 'darwin') app.quit();
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) createWindow();
});

//================================================
// 9. GESTION SQUIRREL
//================================================
function handleSquirrelEvent() {
  if (process.argv.length === 1) {
    return false;
  }

  const ChildProcess = require('child_process');
  const path = require('path');

  const appFolder = path.resolve(process.execPath, '..');
  const rootAtomFolder = path.resolve(appFolder, '..');
  const updateDotExe = path.resolve(path.join(rootAtomFolder, 'Update.exe'));
  const exeName = path.basename(process.execPath);

  const spawn = function(command, args) {
    let spawnedProcess, error;
    try {
      spawnedProcess = ChildProcess.spawn(command, args, { detached: true });
    } catch (error) {}
    return spawnedProcess;
  };

  const spawnUpdate = function(args) {
    return spawn(updateDotExe, args);
  };

  const squirrelEvent = process.argv[1];
  switch (squirrelEvent) {
    case '--squirrel-install':
    case '--squirrel-updated':
      spawnUpdate(['--createShortcut', exeName]);
      setTimeout(app.quit, 1000);
      return true;

    case '--squirrel-uninstall':
      spawnUpdate(['--removeShortcut', exeName]);
      setTimeout(app.quit, 1000);
      return true;

    case '--squirrel-obsolete':
      app.quit();
      return true;
  }
}

if (handleSquirrelEvent()) {
  return;
}