// mareaTides.js
const axios = require('axios');
const fs = require('fs');
const path = require('path');

class MareaTidesManager {
    constructor(apiKey, userDataPath) {
        if (!apiKey || !userDataPath) {
            throw new Error('API key et userDataPath sont requis');
        }

        this.API_KEY = apiKey;
        this.dataFilePath = path.join(userDataPath, 'mareaData.json');
        this.MAREA_API_URL = 'https://api.marea.ooo/v2/tides';
        this.TARGET_LEVEL = 2.18;
        this.CACHE_DURATION = 5 * 24 * 60 * 60 * 1000; // 5 jours
        this.FORECAST_DURATION = 10080; // 7 jours de prévision

        console.log('Fichier de cache:', this.dataFilePath);
        this.logCacheStatus();
    }

    logCacheStatus() {
        try {
            if (fs.existsSync(this.dataFilePath)) {
                const stats = fs.statSync(this.dataFilePath);
                const data = JSON.parse(fs.readFileSync(this.dataFilePath, 'utf8'));
                console.log('Information fichier cache:');
                console.log('- Emplacement:', this.dataFilePath);
                console.log('- Taille:', (stats.size / 1024).toFixed(2), 'KB');
                console.log('- Creation le:', stats.birthtime.toLocaleString());
                console.log('- Derniere modification:', stats.mtime.toLocaleString());
                console.log('- Data valides jusqu au:', 
                    new Date(data.timestamp + this.CACHE_DURATION).toLocaleString());
            } else {
                console.log('Aucun fichier cache trouvé à:', this.dataFilePath);
            }
        } catch (error) {
            console.error('Erreur lors de la vérification du fichier cache:', error);
        }
    }

    async getTidesData(latitude, longitude) {
        if (this.hasValidCachedData()) {
            console.log('Utilisation du cache existant (pas d appel API necessaire)');
            return this.getCachedData();
        }

        console.log('Cache non valide ou expiré - Appel API nécessaire');
        try {
            const response = await axios.get(this.MAREA_API_URL, {
                headers: {
                    'x-marea-api-token': this.API_KEY
                },
                params: {
                    latitude: latitude,
                    longitude: longitude,
                    duration: this.FORECAST_DURATION,
                    interval: 10,
                    datum: 'MLLW',
                    model: 'FES2014'
                }
            });

            const processedData = this.processApiResponse(response.data);
            this.saveTidesData(processedData);
            
            console.log('Nouvelles données enregistrées le:', new Date().toLocaleString());
            return processedData;

        } catch (error) {
            console.error('Erreur lors de l\'appel API:', error);
            if (fs.existsSync(this.dataFilePath)) {
                console.log('Utilisation du cache existant suite à l\'erreur');
                return this.getCachedData();
            }
            throw error;
        }
    }

    processApiResponse(data) {
        const currentDate = new Date();
        return {
            timestamp: currentDate.getTime(),
            heights: data.heights.map(h => ({
                timestamp: h.timestamp,
                height: Number(h.height.toFixed(3)),
                datetime: new Date(h.timestamp * 1000).toISOString()
            })),
            crossings: this.findLevelCrossings(data.heights, this.TARGET_LEVEL),
            source: data.source,
            lastUpdate: currentDate.toISOString()
        };
    }

    findLevelCrossings(heights, targetLevel) {
        const crossings = [];
        
        for (let i = 0; i < heights.length - 1; i++) {
            const current = heights[i];
            const next = heights[i + 1];
            
            if ((current.height <= targetLevel && next.height >= targetLevel) ||
                (current.height >= targetLevel && next.height <= targetLevel)) {
                
                const ratio = (targetLevel - current.height) / (next.height - current.height);
                const timestamp = current.timestamp + ratio * (next.timestamp - current.timestamp);
                
                crossings.push({
                    timestamp: timestamp,
                    datetime: new Date(timestamp * 1000).toISOString(),
                    direction: current.height < next.height ? 'ascending' : 'descending'
                });
            }
        }
        
        return crossings;
    }

    hasValidCachedData() {
        if (!fs.existsSync(this.dataFilePath)) return false;

        try {
            const data = this.getCachedData();
            const now = new Date().getTime();
            const dataAge = now - data.timestamp;
            const isValid = dataAge < this.CACHE_DURATION;
            
            if (!isValid) {
                const daysSinceUpdate = Math.round(dataAge / (24 * 60 * 60 * 1000));
                console.log(`Cache expiré (${daysSinceUpdate} jours) - Mise à jour nécessaire`);
            }
            
            return isValid;
        } catch (error) {
            console.error('Erreur lors de la vérification du cache:', error);
            return false;
        }
    }

    getCachedData() {
        try {
            return JSON.parse(fs.readFileSync(this.dataFilePath, 'utf8'));
        } catch (error) {
            console.error('Erreur lors de la lecture du cache:', error);
            return null;
        }
    }

    saveTidesData(data) {
        try {
            const beforeSave = fs.existsSync(this.dataFilePath) ? fs.statSync(this.dataFilePath) : null;
            
            fs.writeFileSync(this.dataFilePath, JSON.stringify(data, null, 2));
            
            const afterSave = fs.statSync(this.dataFilePath);
            console.log('Mise à jour du fichier cache:');
            console.log('- Nouvelle taille:', (afterSave.size / 1024).toFixed(2), 'KB');
            if (beforeSave) {
                console.log('- Ancienne taille:', (beforeSave.size / 1024).toFixed(2), 'KB');
                console.log('- Ancienne modification:', beforeSave.mtime.toLocaleString());
            }
            console.log('- Nouvelle modification:', afterSave.mtime.toLocaleString());
        } catch (error) {
            console.error('Erreur lors de la sauvegarde du cache:', error);
        }
    }

    getCurrentLevel(heights) {
        if (!heights || heights.length === 0) return null;
        
        const now = Math.floor(Date.now() / 1000);
        const current = heights.reduce((closest, height) => {
            return Math.abs(height.timestamp - now) < Math.abs(closest.timestamp - now) ? height : closest;
        });
        
        return {
            height: current.height.toFixed(2),
            timestamp: current.timestamp,
            datetime: new Date(current.timestamp * 1000).toLocaleString()
        };
    }

    getNextCrossings(crossings) {
        if (!crossings) return [];
        
        const now = Date.now() / 1000;
        return crossings
            .filter(crossing => crossing.timestamp > now)
            .map(crossing => ({
                timestamp: crossing.timestamp,
                datetime: new Date(crossing.timestamp * 1000).toLocaleString(),
                direction: crossing.direction
            }));
    }

    formatTideData(data) {
        return {
            currentLevel: this.getCurrentLevel(data.heights),
            nextCrossings: this.getNextCrossings(data.crossings),
            chartData: data.heights
        };
    }
}

module.exports = MareaTidesManager;