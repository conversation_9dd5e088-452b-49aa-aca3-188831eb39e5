// Variables globales
let currentPlaylist = [];
let savedPlaylists = JSON.parse(localStorage.getItem('savedPlaylists') || '{}');

function initializePlaylistManager() {
    const playlistPopup = document.getElementById('playlist-popup');
    
    // Structure HTML de base
    playlistPopup.innerHTML = `
        <div class="playlist-header">
            <h2>Gestionnaire de Playlist</h2>
            <button id="close-playlist">&times;</button>
        </div>

        <div class="playlist-content">
            <div class="playlist-actions">
                <button id="add-item">Ajouter un média</button>
                <div class="playlist-save">
                    <input type="text" id="playlist-name" placeholder="Nom de la playlist">
                    <button id="save-playlist">Sauvegarder</button>
                </div>
                <div class="playlist-load">
                    <select id="playlist-select"></select>
                    <button id="load-playlist">Charger</button>
                    <button id="delete-playlist">Supprimer</button>
                </div>
            </div>

            <ul id="playlist-items"></ul>

            <div id="add-media-panel" style="display: none;">
                <h3>Ajouter un média</h3>
                <select id="media-type">
                    <option value="image">Image</option>
                    <option value="video">Vidéo</option>
                    <option value="webpage">Page Web</option>
                </select>
                <input type="file" id="file-input">
                <input type="text" id="url-input" placeholder="Ou entrez une URL">
                <input type="number" id="duration-input" placeholder="Durée (secondes)" value="10">
                <div class="panel-buttons">
                    <button id="add-media-cancel">Annuler</button>
                    <button id="add-media-confirm">Ajouter</button>
                </div>
            </div>
        </div>
    `;

    // Style minimal
    const style = document.createElement('style');
    style.textContent = `
        #playlist-popup {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.5);
            width: 80%;
            max-width: 800px;
            max-height: 80vh;
            overflow: auto;
        }

        .playlist-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        #close-playlist {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
        }

        .playlist-actions {
            margin-bottom: 20px;
        }

        #playlist-items {
            list-style: none;
            padding: 0;
        }

        .playlist-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border: 1px solid #ddd;
            margin-bottom: 5px;
        }

        #add-media-panel {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.5);
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            border-radius: 4px;
            color: white;
            background: #333;
            z-index: 10000;
        }
    `;
    document.head.appendChild(style);

    // Gestionnaires d'événements
    setupEventListeners();
    updatePlaylistSelect();
}

function setupEventListeners() {
    // Fermeture
    document.getElementById('close-playlist').addEventListener('click', () => {
        document.getElementById('playlist-popup').style.display = 'none';
    });

    // Ajout de média
    document.getElementById('add-item').addEventListener('click', () => {
        document.getElementById('add-media-panel').style.display = 'block';
    });

    document.getElementById('add-media-cancel').addEventListener('click', () => {
        document.getElementById('add-media-panel').style.display = 'none';
    });

    document.getElementById('add-media-confirm').addEventListener('click', addMedia);

    // Gestion des playlists
    document.getElementById('save-playlist').addEventListener('click', savePlaylist);
    document.getElementById('load-playlist').addEventListener('click', loadPlaylist);
    document.getElementById('delete-playlist').addEventListener('click', deletePlaylist);
}

function addMedia() {
    const type = document.getElementById('media-type').value;
    const fileInput = document.getElementById('file-input');
    const urlInput = document.getElementById('url-input');
    const durationInput = document.getElementById('duration-input');

    if (fileInput.files.length > 0) {
        const file = fileInput.files[0];
        const reader = new FileReader();
        reader.onloadend = function() {
            addItemToPlaylist({
                type,
                content: reader.result,
                duration: parseInt(durationInput.value) * 1000,
                name: file.name
            });
        };
        reader.readAsDataURL(file);
    } else if (urlInput.value) {
        addItemToPlaylist({
            type,
            content: urlInput.value,
            duration: parseInt(durationInput.value) * 1000,
            name: urlInput.value.split('/').pop()
        });
    } else {
        showNotification('Veuillez sélectionner un fichier ou entrer une URL', 'error');
        return;
    }

    document.getElementById('add-media-panel').style.display = 'none';
    resetAddMediaForm();
}

function addItemToPlaylist(item) {
    currentPlaylist.push(item);
    updatePlaylistDisplay();
    showNotification('Média ajouté à la playlist');
}

function updatePlaylistDisplay() {
    const container = document.getElementById('playlist-items');
    container.innerHTML = '';
    
    currentPlaylist.forEach((item, index) => {
        const li = document.createElement('li');
        li.className = 'playlist-item';
        li.innerHTML = `
            <span>${item.name}</span>
            <button onclick="deleteItem(${index})">Supprimer</button>
        `;
        container.appendChild(li);
    });
}

function deleteItem(index) {
    currentPlaylist.splice(index, 1);
    updatePlaylistDisplay();
}

function savePlaylist() {
    const name = document.getElementById('playlist-name').value.trim();
    if (!name) {
        showNotification('Veuillez entrer un nom pour la playlist', 'error');
        return;
    }
    
    savedPlaylists[name] = [...currentPlaylist];
    localStorage.setItem('savedPlaylists', JSON.stringify(savedPlaylists));
    updatePlaylistSelect();
    showNotification('Playlist sauvegardée');
}

function loadPlaylist() {
    const name = document.getElementById('playlist-select').value;
    if (savedPlaylists[name]) {
        currentPlaylist = [...savedPlaylists[name]];
        updatePlaylistDisplay();
        showNotification('Playlist chargée');
    }
}

function deletePlaylist() {
    const name = document.getElementById('playlist-select').value;
    if (savedPlaylists[name]) {
        delete savedPlaylists[name];
        localStorage.setItem('savedPlaylists', JSON.stringify(savedPlaylists));
        updatePlaylistSelect();
        showNotification('Playlist supprimée');
    }
}

function updatePlaylistSelect() {
    const select = document.getElementById('playlist-select');
    select.innerHTML = '';
    Object.keys(savedPlaylists).forEach(name => {
        const option = document.createElement('option');
        option.value = name;
        option.textContent = name;
        select.appendChild(option);
    });
}

function showNotification(message, type = 'success') {
    let notification = document.getElementById('notification');
    if (!notification) {
        notification = document.createElement('div');
        notification.id = 'notification';
        document.body.appendChild(notification);
    }

    notification.textContent = message;
    notification.className = `notification ${type}`;
    notification.style.display = 'block';
    
    setTimeout(() => {
        notification.style.display = 'none';
    }, 3000);
}

function resetAddMediaForm() {
    document.getElementById('file-input').value = '';
    document.getElementById('url-input').value = '';
    document.getElementById('duration-input').value = '10';
}

// Export des fonctions nécessaires
window.initializePlaylistManager = initializePlaylistManager;
window.deleteItem = deleteItem;