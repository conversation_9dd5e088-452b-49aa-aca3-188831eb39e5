// tideService.js

const axios = require('axios');
const fs = require('fs');
const path = require('path');

class TideService {
  constructor(userDataPath) {
    this.tideDataPath = path.join(userDataPath, 'tideData.json');
    this.STORMGLASS_API_KEY = 'VOTRE_CLE_API'; // À remplacer par votre clé API
    this.CACHE_DURATION_DAYS = 3;
  }

  async initializeTideData() {
    if (!fs.existsSync(this.tideDataPath)) {
      await this.writeTideData({ lastUpdate: null, data: [] });
    }
  }

  async readTideData() {
    try {
      const data = fs.readFileSync(this.tideDataPath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      console.error('Erreur lors de la lecture des données:', error);
      return { lastUpdate: null, data: [] };
    }
  }

  async writeTideData(data) {
    try {
      fs.writeFileSync(this.tideDataPath, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error('Erreur lors de l\'écriture des données:', error);
      throw error;
    }
  }

  needsUpdate(lastUpdate) {
    if (!lastUpdate) return true;
    
    const lastUpdateDate = new Date(lastUpdate);
    const now = new Date();
    const lastDataPoint = new Date(lastUpdateDate);
    lastDataPoint.setDate(lastDataPoint.getDate() + this.CACHE_DURATION_DAYS);
    
    // Mettre à jour si nous sommes à moins de 24h de la fin des données
    const oneDayBeforeEnd = new Date(lastDataPoint);
    oneDayBeforeEnd.setDate(oneDayBeforeEnd.getDate() - 1);
    
    return now >= oneDayBeforeEnd;
  }

  async fetchTideData(lat, lng) {
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + this.CACHE_DURATION_DAYS);
    
    const params = {
      lat,
      lng,
      start: new Date().toISOString(),
      end: endDate.toISOString(),
      'source': 'sg',
      'params': 'tideHeight'
    };

    try {
      const response = await axios.get('https://api.stormglass.io/v2/tide/extremes/point', {
        params,
        headers: {
          'Authorization': this.STORMGLASS_API_KEY
        }
      });

      return response.data;
    } catch (error) {
      console.error('Erreur lors de l\'appel à Stormglass:', error);
      throw error;
    }
  }

  async updateTideDataIfNeeded(lat, lng) {
    try {
      await this.initializeTideData();
      const currentData = await this.readTideData();
      
      if (this.needsUpdate(currentData.lastUpdate)) {
        console.log('Mise à jour des données de marée nécessaire');
        const newData = await this.fetchTideData(lat, lng);
        
        await this.writeTideData({
          lastUpdate: new Date().toISOString(),
          data: newData.data,
          meta: {
            lat,
            lng,
            lastFetch: new Date().toISOString()
          }
        });
        
        return { updated: true, data: newData.data };
      }
      
      console.log('Données de marée à jour');
      return { updated: false, data: currentData.data };
    } catch (error) {
      console.error('Erreur lors de la mise à jour des données:', error);
      throw error;
    }
  }

  // Obtenir les prévisions de marée pour une date spécifique
  async getTideDataForDate(date) {
    try {
      const data = await this.readTideData();
      if (!data.data || data.data.length === 0) {
        throw new Error('Aucune donnée de marée disponible');
      }

      const targetDate = new Date(date);
      targetDate.setHours(0, 0, 0, 0);

      return data.data.filter(tide => {
        const tideDate = new Date(tide.time);
        tideDate.setHours(0, 0, 0, 0);
        return tideDate.getTime() === targetDate.getTime();
      });
    } catch (error) {
      console.error('Erreur lors de la récupération des données pour la date:', error);
      throw error;
    }
  }

  // Vérifier si les données sont périmées
  isDataExpired() {
    try {
      const data = this.readTideData();
      return this.needsUpdate(data.lastUpdate);
    } catch {
      return true;
    }
  }
}

module.exports = TideService;