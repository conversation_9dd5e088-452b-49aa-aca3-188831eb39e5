// Fonction pour formater la date en format français
function formatDate(date) {
    const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
    return date.toLocaleDateString('fr-FR', options);
}

// Fonction pour trier les marées par heure
function trierMarees(data) {
    const marees = [];

    if (data["Basse Mer Matin"]) {
        marees.push({ heure: data["Basse Mer Matin"], type: "BASSE", coef: "-" });
    }
    if (data["Haute Mer Matin"]) {
        marees.push({ heure: data["Haute Mer Matin"], type: "HAUTE", coef: data["Coef 1"] });
    }
    if (data["Basse Mer Soir"]) {
        marees.push({ heure: data["Basse Mer Soir"], type: "BASSE", coef: "-" });
    }
    if (data["Haute Mer Soir"]) {
        marees.push({ heure: data["Haute Mer Soir"], type: "HAUTE", coef: data["Coef 2"] });
    }

    return marees.sort((a, b) => a.heure.localeCompare(b.heure));
}

// Fonction pour afficher les informations de marée dans un tableau
function afficherMarees(data, elementId) {
    const container = document.getElementById(elementId);
    const mareesTriees = trierMarees(data);

    let html = `
        <tr>
            <th colspan="3">${formatDate(new Date(data.date))}</th>
        </tr>
        <tr>
            <th>Heure</th>
            <th>Marée</th>
            <th>Coef</th>
        </tr>
    `;

    mareesTriees.forEach(maree => {
        html += `
            <tr>
                <td>${maree.heure}</td>
                <td>${maree.type}</td>
                <td>${maree.coef}</td>
            </tr>
        `;
    });

    container.innerHTML = `<table>${html}</table>`;
    console.log(`Données affichées pour ${elementId}:`, html);
}

// Fonction pour charger et afficher les données
function chargerDonnees() {
    fetch('MareeCNC.json')
        .then(response => {
            console.log('Réponse reçue:', response);
            return response.json();
        })
        .then(data => {
            console.log('Données chargées:', data);
            const aujourdhui = new Date().toISOString().split('T')[0];
            const demain = new Date(Date.now() + 86400000).toISOString().split('T')[0];

            const donneesAujourdhui = data.find(item => item.date === aujourdhui);
            const donneesDemain = data.find(item => item.date === demain);

            if (donneesAujourdhui) {
                afficherMarees(donneesAujourdhui, 'marees-aujourdhui');
            } else {
                // console.log("Aucune donnée pour aujourd'hui");
            }

            if (donneesDemain) {
                afficherMarees(donneesDemain, 'marees-demain');
            } else {
                // console.log("Aucune donnée pour demain");
            }
            
            // Démarrer le carrousel
            startCarousel();
        })
        .catch(error => console.error('Erreur lors du chargement des données:', error));
}

// Fonction pour gérer le carrousel
function startCarousel() {
    const slides = document.querySelectorAll('.slide');
    let currentSlide = 0;

    function showSlide(index) {
        slides.forEach(slide => slide.classList.remove('active'));
        slides[index].classList.add('active');
    }

    function nextSlide() {
        currentSlide = (currentSlide + 1) % slides.length;
        showSlide(currentSlide);
    }

    // Changer de slide toutes les 10 secondes
    setInterval(nextSlide, 7000);
}

// Charger les données au chargement de la page
document.addEventListener('DOMContentLoaded', chargerDonnees);

///// CODE STORMGLASS METEO
