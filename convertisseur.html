<!-- 
 Convertisseur pour recuperer au format json les donneés de marées provenance de info marée puis google sheet puis convertisseur
 Les données sont ensuite traité et envoyés sur le github NautiflixData
 -->

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Convertisseur CSV vers JSON - Marées</title>
    <style>
        body { max-width: 1200px; margin: 20px auto; font-family: sans-serif; padding: 20px; }
        .input-zone, .output-zone { margin: 20px 0; }
        button { margin: 5px; padding: 8px 16px; }
        #debug { background: #f0f0f0; padding: 10px; margin: 10px 0; white-space: pre-wrap; }
        #jsonOutput { background: #f8f8f8; padding: 10px; white-space: pre-wrap; }
    </style>
</head>
<body>
    <h1>Convertisseur CSV vers JSON - Marées</h1>
    
    <div class="input-zone">
        <input type="file" id="csvFile">
        <button onclick="convertCsv()">Convertir</button>
        <button onclick="copyJson()">Copier le JSON</button>
    </div>

    <div id="debug">Les logs apparaîtront ici...</div>
    <div id="jsonOutput">Le JSON apparaîtra ici...</div>

    <script>
        function log(message) {
            const debug = document.getElementById('debug');
            debug.textContent += message + '\n';
        }

        function clearLog() {
            document.getElementById('debug').textContent = '';
        }

        function cleanData(rawContent) {
            // Transformer le contenu en une seule ligne par jour
            const lines = rawContent.split('\n');
            let result = [];
            let currentLine = '';
            let inQuotes = false;
            
            for (let line of lines) {
                line = line.trim();
                if (!line) continue;
                
                // Compte les guillemets dans la ligne
                const quoteCount = (line.match(/"/g) || []).length;
                
                if (!inQuotes && line.startsWith('date')) {
                    continue; // Skip header
                }
                
                if (!inQuotes && /^\d{2}\/\d{2}\/\d{4}/.test(line)) {
                    if (currentLine) {
                        result.push(currentLine);
                    }
                    currentLine = line;
                    inQuotes = (quoteCount % 2 === 1);
                } else {
                    currentLine += '\n' + line;
                    inQuotes = inQuotes ? (quoteCount % 2 === 0) : (quoteCount % 2 === 1);
                }
            }
            
            if (currentLine) {
                result.push(currentLine);
            }
            
            return result;
        }

        function convertCsv() {
            clearLog();
            const file = document.getElementById('csvFile').files[0];
            if (!file) {
                log('Veuillez sélectionner un fichier');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const csv = e.target.result;
                    log('Lecture du fichier OK');

                    // Nettoyer et réorganiser les données
                    const cleanedLines = cleanData(csv);
                    log(`Nombre de jours trouvés: ${cleanedLines.length}`);

                    const result = [];
                    
                    for (let i = 0; i < cleanedLines.length; i++) {
                        const line = cleanedLines[i];
                        try {
                            // Extraire les colonnes
                            let match = line.match(/^([^,]+),("(?:[^"]|"")*"|[^,]*),("(?:[^"]|"")*"|[^,]*),("(?:[^"]|"")*"|[^,]*)$/);
                            if (!match) {
                                log(`Ligne mal formée: ${line}`);
                                continue;
                            }

                            let [_, date, times, heights, coefs] = match;

                            // Nettoyer les données
                            times = times.replace(/"/g, '');
                            heights = heights.replace(/"/g, '');
                            coefs = coefs.replace(/"/g, '');

                            // Extraire les valeurs
                            const timeArray = times.split('\n').map(t => t.trim()).filter(t => t);
                            const heightArray = heights.split('\n')
                                .map(h => h.trim())
                                .map(h => h.replace('m', ''))
                                .map(h => parseFloat(h.replace(',', '.')))
                                .filter(h => !isNaN(h));
                            const coefArray = coefs.split('\n')
                                .map(c => c.trim())
                                .filter(c => c)
                                .map(c => parseInt(c))
                                .filter(c => !isNaN(c));

                            // Créer l'objet marée
                            const tides = timeArray.map((time, idx) => ({
                                time,
                                height: heightArray[idx]
                            }));

                            // Ajouter à la liste
                            result.push({
                                date,
                                tides,
                                coefficient: coefArray
                            });

                            if (i < 2) {
                                log(`\nJour ${i + 1} traité :`);
                                log(`Date: ${date}`);
                                log(`Temps: ${JSON.stringify(timeArray)}`);
                                log(`Hauteurs: ${JSON.stringify(heightArray)}`);
                                log(`Coefficients: ${JSON.stringify(coefArray)}`);
                            }

                        } catch (err) {
                            log(`Erreur jour ${i + 1}: ${err.message}`);
                            log(`Contenu: ${line}`);
                        }
                    }

                    log(`\nConversion terminée. ${result.length} jours traités.`);
                    
                    // Afficher le résultat
                    document.getElementById('jsonOutput').textContent = 
                        JSON.stringify(result, null, 2);

                } catch (err) {
                    log(`Erreur globale: ${err.message}`);
                }
            };

            reader.onerror = function(err) {
                log(`Erreur de lecture: ${err}`);
            };

            reader.readAsText(file);
        }

        function copyJson() {
            const json = document.getElementById('jsonOutput').textContent;
            navigator.clipboard.writeText(json)
                .then(() => alert('JSON copié !'))
                .catch(err => alert('Erreur de copie: ' + err));
        }
    </script>
</body>
</html>