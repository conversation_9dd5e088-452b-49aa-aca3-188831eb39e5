

@import url('https://fonts.googleapis.com/css2?family=Josefin+Sans:ital,wght@0,100..700;1,100..700&display=swap');

body, html {
    margin: 0;
    padding: 0;
    /* font-family: "Josef<PERSON> Sans", sans-serif; */
    /* font-family: 'Hind', sans-serif; */
    font-family: 'Pilcrow Rounded', sans-serif;
    background-color: #f0f8ff;
    height: 100%;
    overflow: hidden;
    color: #333;
}

:root {
    --primary-color: #346D83;
    --secondary-color: #D9E4E3;
    --tertiary-color: #7DACB7;
    --button-colorActive: #B47A58; 
    --button-colorInactive : #DBC2A3; 
    --button-color : #71808B;
  }

  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    background-color: #f0f0f0;
    border-radius: 10px;
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  }
  
  ::-webkit-scrollbar-thumb {
    background-color: var(--button-color);
    border-radius: 10px;
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background-color: #888;
  }
  
  ::-webkit-scrollbar-track {
    background-color: #f0f0f0;
    border-radius: 10px;
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  }
  
  ::-webkit-scrollbar-button {
    background-color: #f0f0f0;
    border-radius: 10px;
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  }
  
  ::-webkit-scrollbar-corner {
    background-color: #f0f0f0;
    border-radius: 10px;
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  }
      

  .material-icons {
    font-size: 24px; /* Ajustez cette valeur selon vos besoins */
    line-height: 1; /* Réduit l'espace vertical autour de l'icône */
    vertical-align:middle; /* Assure un bon alignement vertical */
    padding: 0%;
}

/* Pour les écrans de petite taille */
@media (max-width: 768px) {
  #playlist-popup {
    width: 90%; /* ajustement de la largeur pour les écrans de petite taille */
  }
}
  
  /* Pour les écrans de taille moyenne */
  @media (min-width: 769px) and (max-width: 1024px) {
    #playlist-popup {
      width: 80%;
    }
  }
  
  /* Pour les écrans de grande taille */
  @media (min-width: 1600px) {
    #playlist-popup {
      width: 50%;
    }
  }


#standby-image {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
}

#main-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    position: relative;
    background: linear-gradient(135deg, #e6f2ff 0%, #f0f8ff 100%);
}

#stream-container {
    flex-grow: 1;
    position: relative;
    overflow: hidden;
}


.stream-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

#standby-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 1;
}

#control-area {
    position: fixed; 
    bottom: -80px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(197, 219, 231, 0.438);
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    transition: bottom 0.3s ease-in-out;
    /* box-shadow: 0 -5px 15px rgba(82, 80, 80, 0.651); */
    border-radius: 20px 20px 0 0;
    backdrop-filter: blur(10px);
    width: fit-content;
    max-width: 1000px;
}

#control-area::before {
    content: '';
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    width: fit-content;
    height: 30px;
    background-color: rgba(69, 73, 77, 0.0.205);
    /* box-shadow: 0 -5px 15px rgba(0, 0, 0, 0.1); */
    border-radius: 30px 30px 0 0;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #ffffff;
    backdrop-filter: blur(10px);
    font-size: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
}

#control-area:hover::before {
    top: -35px;
    height: 35px;
}

#control-area-trigger {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 120px;
    z-index: 999;
}

#control-area-trigger:hover + #control-area,
#control-area:hover {
    bottom: 0;
}

.control-button, .nav-button, #fullscreen-button {
    display: flex;
    align-items: center; /* Aligne les éléments verticalement */
    justify-content: center; /* Centre les éléments horizontalement */
    background-color: var(--button-color);
    color: #ffffff;
    border: none;
    padding: 12px 24px;
    margin: 0 10px;
    cursor: pointer;
    font-size: 14px;
    border-radius: 30px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    font-family: "Josefin Sans", sans-serif;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
}

.control-button:hover, .nav-button:hover, #fullscreen-button:hover {
    background-color: #2a6285;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

#navigation-controls {
    display: flex;
}


#control-button :hover{
    background-color: #2a8558;}

#reset-stream:hover {
    background-color: #dd9e15;
    transform: translateY(-2px);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

#playlist-popup {
  display: none;
  position: fixed;
  top: 50%; /* positionnement vertical */
  left: 50%; /* positionnement horizontal */
  transform: translate(-50%, -50%); /* décalage de 50% de la largeur et de la hauteur */
  background-color: rgba(211, 211, 211, 0.692);
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 0 20px rgba(0,0,0,0.3);
  z-index: 1001;
  min-width: 300px; /* taille minimale */
  max-width: 70%;
  max-height: 70%;
  overflow-y: auto;
  width: 77%;
  font-size: 16px; /* taille de police relative */
}

.playlist-columns {
  display: grid;
  grid-template-columns: 65% 35%;
  gap: 20px;
  margin-top: 20px;
}

.management-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 15px;
}


#playlist-items {
    list-style-type: none;
    padding: 15px;
}

#playlist-items li {
    margin-bottom: 15px;
    padding: 10px;
    background-color: #f7f7f7;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

#playlist-items li:hover {
    background-color: #e0e0e0;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

#close-playlist {
    position: sticky;
    top: 0;
    right: 0;
    float: right;
    width: 50px;
    height: 50px;
    background-color: var(--button-color);
    color: #ffe261;
    border: none;
    border-radius: 50%;
    font-size: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    z-index: 1002;
    margin-bottom: 10px;
}

#close-playlist:hover {
    background-color: #e7be08;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    color: white;   
}

#add-item-form, #playlist-management {
    margin-top: 25px;
    padding: 20px;
    background-color: #f7f7f7;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

#add-item-form input, #add-item-form select, #add-item-form textarea,
#playlist-management input, #playlist-management select {
    margin: 10px 0;
    padding: 10px;
    width: 100%;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

#add-item-form input:focus, #add-item-form select:focus, #add-item-form textarea:focus,
#playlist-management input:focus, #playlist-management select:focus {
    border-color: #0077be;
    outline: none;
}

#add-item-form button, #playlist-management button {
    margin-top: 15px;
    padding: 10px 20px;
    background-color: #4CAF50;
    color: white;
    border: none;
    cursor: pointer;
    border-radius: 5px;
    font-size: 16px;
    transition: all 0.3s ease;
}

#add-item-form button:hover, #playlist-management button:hover {
    background-color: #1163fcef;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.delete-item, .save-duration {
    background-color: #ff4136;
    color: white;
    border: none;
    padding: 8px 15px;
    cursor: pointer;
    border-radius: 5px;
    margin-left: 10px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.delete-item:hover, .save-duration:hover {
    background-color: #d0342b;
    /* transform: translateY(-2px); */
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.save-duration {
    background-color: #4CAF50;
}

.save-duration:hover {
    background-color: #45a049;
}

.duration-input {
    width: 80px;
    margin-right: 10px;
    text-align: center;
}

.playlist-item-content {
    flex-grow: 1;
    margin-right: 15px;
    font-size: 16px;
}

.playlist-item-controls {
    display: flex;
    align-items: center;
}

#playlist-select {
    margin-bottom: 15px;
    width: 100%;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #ddd;
    font-size: 16px;
}

h2, h3 {
    color: #0077be;
    margin-bottom: 10px;
}

#playlist-label {
color:  #0077be;
background-color: #d4d4d4;
padding: 10px 10px;
border-radius: 5px; 
font-size: 1.2rem;
} 


.section-divider {
    border-top: 1px solid #ddd;
    margin: 30px 0;
}

#notification {
    position:fixed;
    top: 2%;
    left: 40%;
    background-color: #005ec9;
    color: white;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    display: none;
    z-index: 1002;
    transition: opacity 0.3s ease;
}

.duration-container {
    display: flex;
    align-items: center;
    margin-top: 10px;
}

#duration-label {
    margin-right: 10px;
}

#iframe-name-input, #webpage-url-input {
    display: none;
    /* margin-top: 10px; */
}



#fullscreen-button:hover {
    background-color: #005c8f;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

#timer {
    position: absolute;
    bottom:  0% ;
    right: 0px;
    background-color: rgba(145, 145, 145, 0.13);
    color: rgba(107, 107, 107, 0.459);
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 14px;
    z-index: 1000;
}

#info-panel {
    position: absolute;
    top: 2%;
    right: 10px;
    width: 245px;
    height: auto;
    background-color: rgba(207, 207, 207, 0.781);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    z-index: 1000;
    display: none;
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
}

#info-panel.active {
    display: block;
}

#info-panel h3 {
    margin-top: 0;
    color: #01324e;
    font-size: 1.1em;
    border-bottom: 2px solid #01324e;
    padding-bottom: 5px;
}

#info-panel p {
    margin: 10px 0;
    font-size: 1.1em;
}


#toggle-info-panel:hover {
    background-color: #45a049;
    /* transform: translateY(-2px); */
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

#toggle-info-panel.active {
    background-color: var(--button-color);
}

#toggle-info-panel.active:hover {
    background-color: #d32f2f;
}

#tide-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 5px 10px;
    margin-top: 15px;
}

#tide-table th, #tide-table td {
    text-align: left;
    padding: 10px;
    background-color: rgba(0, 119, 190, 0.1);
    border-radius: 5px;
}

#tide-table th {
    background-color: #0077be;
    color: white;
    font-weight: bold;
}

#tide-table tr:nth-child(even) td {
    background-color: rgba(0, 162, 255, 0.301);
}

.tide-time {
    font-weight: bold;
    color: #0077be;
}

.tide-height {
    color: #333;
}

#marees-container {
    margin-top: 20px;
} 

#marees-container table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

#marees-container th, #marees-container td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

#marees-container th {
    background-color: #f2f2f2;
}

.carousel {
    position: relative;
    width: 100%;
    height: 100%;
    perspective: 1000px;
}

.slide {
    display: none;
    width: 100%;
    height: 100%;
    /* transition: opacity 0.5s ease-in-out; */
    backface-visibility: hidden;
    transform-style: preserve-3d;
    transition: transform 1s ease-in-out, opacity 1s ease-in-out;
    opacity: 0;
}

.slide.active {
    opacity: 1;
    display: block;
}

/* ==========================================================================
   13. Composants Météo - Design Minimaliste
   ========================================================================== */

#devref {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: x-small!important;
  text-align: center!important;
  margin-top: 0px;
}

.logo-menu {
  display: block;
  margin: 0 auto;
  width: 100px;
  height: 100px;
  position: relative;
  bottom: 0;
}

  #date-time {
    font-size: 2em !important;
    font-weight: bold;
    color: #535353;
    margin-bottom: 10px;
    text-align: center;
  }

  #time {
    font-size: 1.2em !important;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
    text-align: center;
  } 

#weather-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: 10px;
    border-radius: 10px;
    /* box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); */
  }
  
  #weather-description {
    font-size: 1.2em !important; 
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
  }
  
  #weather-icon {
    width: 80px;
    height: 80px;
  }
  
  #weather-temperature {
    font-size: 1.2em  !important;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
  }
  
  #weather-wind, #weather-humidity, #weather-visibility {
    font-size: 1em;
    color: #555a72;
    margin-bottom: 10px;
    white-space: pre-line; /* Permet les retours à la ligne dans le texte */
  }
  
  #weather-wind {
    font-weight: bold;
  }
  
  #weather-info p {
    margin: 0;
    padding: 0;
    border: none;
    outline: none;
    background: none;
    text-align: center;
  }

  /* Section soleil */
#sun-section {
  margin-top: 10px;
}

.sun-times {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  font-size: 0.9rem;
  color: #666;
}

.sun-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.sun-item span {
  color: #333;
  font-weight: 500;
}
  
  @media (max-width: 768px) {
    #weather-info {
      padding: 10px;
    }
    #weather-icon {
      width: 50px;
      height: 50px;
    }
    #weather-temperature {
      font-size: 24px;
    }
    #weather-wind, #weather-humidity, #weather-visibility {
      font-size: 14px;
    }
  }

  .move-button,.delete-button,.duplicate-button {
  display: flex;
  margin: 5px;
}
  .move-button {
    background-color: #4CAF50;
    color: #fff;
    border: none;
    padding: 5px 10px;
    font-size: 16px;
    cursor: pointer;
    border-radius: 5px;
    transition: background-color 0.3s ease;
  }
  
  .move-button:hover {
    background-color: #3e8e41;
  }
  
  .delete-button {
    background-color: #b80c54;
    color: #fff;
    border: none;
    padding: 5px 10px;
    font-size: 16px;
    cursor: pointer;
    border-radius: 5px;
    transition: background-color 0.3s ease;
  }
  
  .delete-button:hover {
    background-color: #e91e63;
  }
  
  .duplicate-button {
    background-color: #03A9F4;
    color: #fff;
    border: none;
    padding: 5px 10px;
    font-size: 16px;
    cursor: pointer;
    border-radius: 5px;
    transition: background-color 0.3s ease;
  }
  
  .duplicate-button:hover {
    background-color: #0288d1;
  }

  /* fin style meteo */