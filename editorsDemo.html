<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nautiflix - Éditeurs Intégrés</title>
    
    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Styles des éditeurs -->
    <link rel="stylesheet" href="editorsStyles.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #2c5aa0 0%, #87ceeb 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .demo-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 40px;
            text-align: center;
            max-width: 800px;
            width: 100%;
        }
        
        .demo-title {
            color: #2c5aa0;
            margin-bottom: 20px;
            font-size: 2.5em;
            font-weight: 300;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .demo-subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 1.2em;
        }
        
        .editors-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 40px 0;
        }
        
        .editor-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 30px;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .editor-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border-color: #2c5aa0;
        }
        
        .editor-icon {
            font-size: 3em;
            color: #2c5aa0;
            margin-bottom: 20px;
        }
        
        .editor-title {
            font-size: 1.5em;
            color: #333;
            margin-bottom: 15px;
            font-weight: 500;
        }
        
        .editor-description {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .editor-features {
            text-align: left;
            margin-bottom: 25px;
        }
        
        .editor-features ul {
            list-style: none;
            padding: 0;
        }
        
        .editor-features li {
            padding: 5px 0;
            display: flex;
            align-items: center;
            gap: 10px;
            color: #555;
        }
        
        .editor-features li i {
            color: #28a745;
            width: 16px;
        }
        
        .demo-button {
            background: linear-gradient(135deg, #2c5aa0 0%, #87ceeb 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            font-size: 16px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(44, 90, 160, 0.3);
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 0 auto;
        }
        
        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(44, 90, 160, 0.4);
        }
        
        .integration-info {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
            border-left: 4px solid #2196f3;
        }
        
        .integration-info h3 {
            color: #1976d2;
            margin-bottom: 10px;
        }
        
        .integration-info p {
            color: #424242;
            line-height: 1.6;
        }
        
        .current-widgets {
            background: #fff3cd;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            border-left: 4px solid #ffc107;
        }
        
        .current-widgets h3 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        .widgets-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        
        .widget-item {
            background: white;
            padding: 10px;
            border-radius: 6px;
            border: 1px solid #ffeaa7;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .widget-item i {
            color: #f39c12;
        }
        
        @media (max-width: 768px) {
            .editors-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .demo-title {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">
            <i class="fas fa-anchor"></i>
            Nautiflix V3
        </h1>
        <p class="demo-subtitle">Éditeurs Intégrés pour Contenu Personnalisé</p>
        
        <div class="editors-grid">
            <!-- Éditeur Météo -->
            <div class="editor-card">
                <div class="editor-icon">
                    <i class="fas fa-cloud-sun"></i>
                </div>
                <h3 class="editor-title">Éditeur Météo</h3>
                <p class="editor-description">
                    Créez des écrans météo personnalisés avec vos données Windguru et Arome préférées.
                </p>
                <div class="editor-features">
                    <ul>
                        <li><i class="fas fa-check"></i> Templates basés sur vos widgets existants</li>
                        <li><i class="fas fa-check"></i> Données Windguru/Arome en temps réel</li>
                        <li><i class="fas fa-check"></i> Interface drag & drop intuitive</li>
                        <li><i class="fas fa-check"></i> Personnalisation complète des couleurs</li>
                        <li><i class="fas fa-check"></i> Prévisualisation en temps réel</li>
                    </ul>
                </div>
                <button class="demo-button" onclick="openMeteoEditor()">
                    <i class="fas fa-edit"></i>
                    Ouvrir l'Éditeur Météo
                </button>
            </div>
            
            <!-- Éditeur de Pages -->
            <div class="editor-card">
                <div class="editor-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
                <h3 class="editor-title">Éditeur de Pages</h3>
                <p class="editor-description">
                    Créez des pages d'information personnalisées : horaires, annonces, événements.
                </p>
                <div class="editor-features">
                    <ul>
                        <li><i class="fas fa-check"></i> Éditeur WYSIWYG intégré</li>
                        <li><i class="fas fa-check"></i> Blocs modulaires (texte, tableaux, images)</li>
                        <li><i class="fas fa-check"></i> Templates prédéfinis</li>
                        <li><i class="fas fa-check"></i> Gestion des horaires de cours</li>
                        <li><i class="fas fa-check"></i> Système d'annonces</li>
                    </ul>
                </div>
                <button class="demo-button" onclick="openInfoEditor()">
                    <i class="fas fa-edit"></i>
                    Ouvrir l'Éditeur de Pages
                </button>
            </div>
        </div>
        
        <div class="current-widgets">
            <h3><i class="fas fa-puzzle-piece"></i> Vos Widgets Actuels</h3>
            <p>Ces widgets seront remplacés par les éditeurs intégrés :</p>
            <div class="widgets-list">
                <div class="widget-item">
                    <i class="fas fa-calendar-week"></i>
                    <span>meteoSemaine.html</span>
                </div>
                <div class="widget-item">
                    <i class="fas fa-sun"></i>
                    <span>meteotoday.html</span>
                </div>
                <div class="widget-item">
                    <i class="fas fa-wind"></i>
                    <span>vent6H.html</span>
                </div>
                <div class="widget-item">
                    <i class="fas fa-clock"></i>
                    <span>Pages horaires statiques</span>
                </div>
            </div>
        </div>
        
        <div class="integration-info">
            <h3><i class="fas fa-cogs"></i> Intégration dans Nautiflix</h3>
            <p>
                Ces éditeurs s'intègrent directement dans votre système de playlist existant. 
                Créez vos contenus personnalisés et ajoutez-les comme nouveaux types de médias 
                dans vos playlists. Plus besoin de modifier des fichiers HTML !
            </p>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="meteoEditor.js"></script>
    <script src="infoPageEditor.js"></script>
    
    <script>
        function openMeteoEditor() {
            // Créer et afficher l'éditeur météo
            const editor = new MeteoEditor();
            const editorHTML = editor.createEditor();
            
            // Ajouter au DOM
            const editorContainer = document.createElement('div');
            editorContainer.innerHTML = editorHTML;
            document.body.appendChild(editorContainer.firstElementChild);
            
            // Initialiser l'éditeur
            editor.initializeEditor();
        }
        
        function openInfoEditor() {
            // Créer et afficher l'éditeur de pages
            const editor = new InfoPageEditor();
            const editorHTML = editor.createEditor();
            
            // Ajouter au DOM
            const editorContainer = document.createElement('div');
            editorContainer.innerHTML = editorHTML;
            document.body.appendChild(editorContainer.firstElementChild);
            
            // Initialiser l'éditeur
            editor.initializeEditor();
        }
        
        // Simulation de données météo pour la démo
        window.addEventListener('load', function() {
            // Ajouter quelques configurations de démonstration
            const demoMeteoConfigs = {
                'Météo Semaine CNC': {
                    name: 'Météo Semaine CNC',
                    type: 'meteo-custom',
                    config: {
                        layout: 'semaine',
                        dataSource: 'windguru',
                        widgets: [
                            { type: 'weekForecast', config: { size: 'large' } },
                            { type: 'wind', config: { size: 'medium' } },
                            { type: 'temperature', config: { size: 'medium' } }
                        ]
                    }
                }
            };
            
            const demoInfoPages = {
                'Horaires Cours Voile': {
                    name: 'Horaires Cours Voile',
                    type: 'info-page',
                    config: {
                        title: 'Horaires des Cours de Voile',
                        type: 'horaires',
                        blocks: [
                            { type: 'title', content: '<h1>Horaires des Cours</h1>' },
                            { type: 'schedule', content: '...' }
                        ]
                    }
                }
            };
            
            localStorage.setItem('meteoConfigs', JSON.stringify(demoMeteoConfigs));
            localStorage.setItem('infoPages', JSON.stringify(demoInfoPages));
        });
        
        // Gestion des raccourcis clavier
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                // Fermer les éditeurs ouverts
                const editors = document.querySelectorAll('.meteo-editor-container, .info-editor-container');
                editors.forEach(editor => {
                    if (editor.parentNode) {
                        editor.parentNode.removeChild(editor);
                    }
                });
            }
        });
    </script>
</body>
</html>
