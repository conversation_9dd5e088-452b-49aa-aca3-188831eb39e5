<html><head><base href="Meteo des vents">
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Prévisions du Vent - Agon-Coutainville</title>
<style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-family: "Segoe UI", system-ui, -apple-system, sans-serif;
      }

      body {
        background: linear-gradient(135deg, #0e4e74 0%, #05316b 100%);
        max-width: calc(100vw - 250px);
        min-height: 100vh;
        margin: 0;
        padding: 20px;
        color: #e6e6e6;
        overflow-x: hidden;
      }
  .container {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    box-sizing: border-box;
    padding: 20px;
  }
  h1 {
    font-size: 2.2em;
    padding-bottom: 10px;
    margin: 0 0 10px 0;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
  }

  p {
    font-size: 1.5vw;
    margin: 0 0 20px 0;
  }
  #chartContainer {
    flex-grow: 1;
    position: relative;
    width: 100%;
  }
  canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100% !important;
    height: 100% !important;
  }
</style>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <div class="container">
    <h1>Prévisions du Vent : 7 prochaines heures</h1>
    <div id="chartContainer">
      <canvas id="windChart"></canvas>
    </div>
  </div>

  <script>
    async function fetchWindData() {
      const response = await fetch('https://api.open-meteo.com/v1/forecast?latitude=49.0292&longitude=-1.6&hourly=wind_speed_10m,wind_direction_10m,wind_gusts_10m&wind_speed_unit=kn&timezone=Europe%2FBerlin&forecast_days=1&models=meteofrance_seamless');
      const data = await response.json();
      return data;
    }

    function createWindArrow(ctx, centerX, centerY, direction, size) {
      ctx.save();
      ctx.translate(centerX, centerY);

      // Draw white circle with padding
      const padding = 3;
      ctx.beginPath();
      ctx.arc(0, 0, size/2 + padding, 0, 2 * Math.PI);
      ctx.fillStyle = '#FFFFFF';
      ctx.fill();

      // Draw arrow
      ctx.rotate((direction - 180) * Math.PI / 180);
      ctx.beginPath();
      ctx.moveTo(0, -size/2);
      ctx.lineTo(size/4, size/2);
      ctx.lineTo(0, size/4);
      ctx.lineTo(-size/4, size/2);
      ctx.closePath();
      ctx.fillStyle = '#2C3E50';
      ctx.fill();
      
      ctx.restore();
    }

    function getCardinalDirection(angle) {
      const directions = ['N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE', 'S', 'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW'];
      return directions[Math.round(angle / 22.5) % 16];
    }

    function createWindChart(labels, windSpeeds, windGusts, windDirections) {
      const ctx = document.getElementById('windChart').getContext('2d');
      const chart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: labels,
          datasets: [
            {
              label: 'Vitesse du vent (nœuds)',
              data: windSpeeds,
              borderColor: '#FFFFFF',
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              borderWidth: 4,
              pointBackgroundColor: '#FFFFFF',
              pointBorderColor: '#FFFFFF',
              pointHoverBackgroundColor: '#FFFFFF',
              pointHoverBorderColor: '#FFFFFF',
              pointRadius: 6,
              pointHoverRadius: 8,
              tension: 0.4,
              fill: true
            },
            {
              label: 'Rafales (nœuds)',
              data: windGusts,
              borderColor: '#FFD700',
              backgroundColor: 'rgba(255, 215, 0, 0.2)',
              borderWidth: 4,
              pointBackgroundColor: '#FFD700',
              pointBorderColor: '#FFD700',
              pointHoverBackgroundColor: '#FFD700',
              pointHoverBorderColor: '#FFD700',
              pointRadius: 6,
              pointHoverRadius: 8,
              tension: 0.4,
              fill: true
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          layout: {
            padding: {
              top: 5  // Ajout d'un padding de 5px en haut du graphique
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: 'Vitesse (nœuds)',
                font: {
                  size: 18,
                  weight: 'bold'
                },
                color: '#FFFFFF'
              },
              ticks: {
                font: {
                  size: 16
                },
                color: '#FFFFFF'
              },
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              }
            },
            x: {
              title: {
                display: true,
                text: 'Heure',
                font: {
                  size: 18,
                  weight: 'bold'
                },
                color: '#FFFFFF'
              },
              ticks: {
                font: {
                  size: 16
                },
                color: '#FFFFFF'
              },
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              }
            }
          },
          plugins: {
            legend: {
              labels: {
                font: {
                  size: 16
                },
                color: '#FFFFFF'
              }
            },
            tooltip: {
              titleFont: {
                size: 16
              },
              bodyFont: {
                size: 14
              },
              callbacks: {
                label: function(context) {
                  let label = context.dataset.label || '';
                  if (label) {
                    label += ': ';
                  }
                  label += context.parsed.y.toFixed(1) + ' nœuds';
                  return label;
                },
                afterBody: function(context) {
                  const dataIndex = context[0].dataIndex;
                  const direction = windDirections[dataIndex];
                  const cardinalDirection = getCardinalDirection(direction);
                  return `Direction: ${cardinalDirection} (${direction.toFixed(0)}°)`;
                }
              }
            }
          }
        },
        plugins: [{
          id: 'windArrows',
          afterDatasetsDraw(chart, args, pluginOptions) {
            const {ctx, chartArea: {top, bottom, left, right, width, height}} = chart;

            ctx.save();

            for (let i = 0; i < chart.data.labels.length; i++) {
              const dataset = chart.data.datasets[0];
              const meta = chart.getDatasetMeta(0);
              const xPos = meta.data[i].x;
              const yPos = meta.data[i].y;

              createWindArrow(ctx, xPos, yPos, windDirections[i], 20);

              ctx.textAlign = 'center';
              ctx.fillStyle = 'white';
              ctx.font = 'bold 12px Arial';
              ctx.fillText(getCardinalDirection(windDirections[i]), xPos, yPos - 20);
              ctx.font = '10px Arial';
              ctx.fillText(`${windDirections[i].toFixed(0)}°`, xPos, yPos + 20);
            }

            ctx.restore();
          }
        }]
      });
    }

    async function init() {
      const data = await fetchWindData();
      const currentHour = new Date().getHours();
      const labels = data.hourly.time.slice(currentHour, currentHour + 7).map(time => {
        const date = new Date(time);
        return date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
      });
      const windSpeeds = data.hourly.wind_speed_10m.slice(currentHour, currentHour + 7);
      const windGusts = data.hourly.wind_gusts_10m.slice(currentHour, currentHour + 7);
      const windDirections = data.hourly.wind_direction_10m.slice(currentHour, currentHour + 7);
      createWindChart(labels, windSpeeds, windGusts, windDirections);
    }

    init();

    // Resize chart when window is resized
    window.addEventListener('resize', function() {
      init();
    });
  </script>
</body>
</html>