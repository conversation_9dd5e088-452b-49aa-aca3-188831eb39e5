<header 
style="
position: absolute;
top: 20px;
left: 40%;
font-family: 'Segoe UI', sans-serif;
font-size: 24px;
font-weight: 700;
color: #333;
background-color: #f7f7f7;
border-radius: 5px;
padding: 15px 30px;
margin: 10px;
text-align: center;
box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
"

  <span>Période des vagues en secondes</span>
</header>
<iframe id="ventusky-iframe" width="100%" height="100%" src="" frameborder="0"></iframe>
 <script>

const heuresDecalees = {
  "0000": "0000",
  "0300": "0300",
  "0600": "0600",
  "0900": "0900",
  "1200": "1200",
  "1500": "1500",
  "1800": "1800",
  "2100": "2100"
};

let date = `${new Date().getFullYear()}${(new Date().getMonth() + 1).toString().padStart(2, '0')}${new Date().getDate().toString().padStart(2, '0')}`;

let index = Object.keys(heuresDecalees).findIndex(key => key === `${new Date().getHours().toString().padStart(2, '0')}${new Date().getMinutes().toString().padStart(2, '0')}`);

function rafraichirIframe() {
  const iframe = document.getElementById('ventusky-iframe');
  iframe.style.opacity = 0;
  setTimeout(() => {
    const url = `https://www.ventusky.com/?p=49.030;-1.556;11&l=wind-wave-period&t=${date}/${heuresDecalees[Object.keys(heuresDecalees)[index]]}`;
    iframe.src = url;
    iframe.style.opacity = 1;
    index = (index + 1) % Object.keys(heuresDecalees).length;
    if (index === 0) {
      date = `${new Date().getFullYear()}${(new Date().getMonth() + 1).toString().padStart(2, '0')}${new Date().getDate().toString().padStart(2, '0')}`;
    }
  }, 100);
  setTimeout(rafraichirIframe, 5000);
}

// Premier rafraîchissement pour passer au créneau suivant
index = (index + 1) % Object.keys(heuresDecalees).length;
const url = `https://www.ventusky.com/?p=49.030;-1.556;11&l=wind-wave-period&t=${date}/${heuresDecalees[Object.keys(heuresDecalees)[index]]}`;
document.getElementById('ventusky-iframe').src = url;

rafraichirIframe();

document.body.style.background = 'black';
document.getElementById('ventusky-iframe').style.transition = 'opacity 0.5s';

 </script>