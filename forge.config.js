const { FusesPlugin } = require('@electron-forge/plugin-fuses');
const { FuseV1Options, FuseVersion } = require('@electron/fuses');

module.exports = {
  packagerConfig: {
    asar: true,
    icon: './Assets/icon.ico',
    extraResource: [
      './Assets',
      './widgets',
      'MareeCNC.json',
    ]
  },
  rebuildConfig: {},
  makers: [
    {
      name: '@electron-forge/maker-squirrel',
      config: {
        name: 'Nautiflix',
        authors: 'Stimquest',
        exe: 'Nautiflix.exe',
        setupIcon: './Assets/icon.ico',
        setupExe: 'NautiflixSetup.exe',
        loadingGif: './assets/setupNautiflix.gif',
        noMsi: true,
        createDesktopShortcut: true,
        shortcutLocations: ['StartMenu', 'Desktop'],
      },
    },
  ],
  plugins: [
    {
      name: '@electron-forge/plugin-auto-unpack-natives',
      config: {},
    },
    new FusesPlugin({
      version: FuseVersion.V1,
      [FuseV1Options.RunAsNode]: false,
      [FuseV1Options.EnableCookieEncryption]: true,
      [FuseV1Options.EnableNodeOptionsEnvironmentVariable]: false,
      [FuseV1Options.EnableNodeCliInspectArguments]: false,
      [FuseV1Options.EnableEmbeddedAsarIntegrityValidation]: true,
      [FuseV1Options.OnlyLoadAppFromAsar]: false,
      [FuseV1Options.LoadExternalResources]: true,
    }),
  ],
};