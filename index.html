<html><head>
    <meta charset="UTF-8">
    <!-- <link rel="stylesheet" href="node_modules/material-design-icons-iconfont/dist/material-design-icons.css"> -->
    <link rel="stylesheet" href="./style.css">
    <link rel="stylesheet" href="./stylePopup.css">
    <link rel="stylesheet" href="./editorsStyles.css">
    <link href="https://api.fontshare.com/v2/css?f[]=hind@400&f[]=pilcrow-rounded@600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="./node_modules/@fortawesome/fontawesome-free/css/all.min.css">
</head>
  <body>

  <div id="main-container">
      <div id="stream-container">
        <div id="standby-image"></div>
          <div id="stream-content-1" class="stream-content"></div>
          <div id="stream-content-2" class="stream-content"></div>
          <div id="timer"></div>
          <div id="info-panel">
              <!-- <h3>Informations</h3> -->
              <p id="date-time"></p>
              <!-- <p id="weather"></p> -->
              <div id="weather-info">
                <p id="weather-description"></p>
                <img id="weather-icon" src="" alt="Icone météo">
                <p id="weather-temperature"></p>
                <p id="weather-wind"></p>
                <p id="weather-humidity"></p>
                <p id="weather-visibility"></p>
                <p id="sea-temperature"></p>
                <div id="tide-info"> </div>
              </div>
                <div id="marees-container">

                    <div id="marea-panel" class="marea-panel">
                        <div id="marea-data"></div>
                    </div>
                    <div id="marees-container">
                  <div id="carousel">
                      <div id="aujourdhui" class="slide active">
                          <h3>Marées : aujourd'hui</h3>
                          <table id="marees-aujourdhui"></table>
                      </div>
                      <div id="demain" class="slide">
                          <h3>Marées : demain</h3>
                          <table id="marees-demain" class="table-maree"></table>
                      </div>
                  </div>
              </div>
            </div>
              <div id="devref">
                <p>Développé par : Stimquest.com <br> Pour le Club Nautique de Coutainville</p>   
              </div>
             <img class="logo-menu" src="./assets/logoCNC.png"  alt="">
            </div>
            </div>
          </div>
      </div>
      <div id="control-area-trigger"></div>
      <div id="control-area">
        <button id="start-stream" class="control-button"><i class="fas fa-play"></i></button>
        <button id="reset-stream" class="control-button"><i class="fas fa-stop"></i></button>
        <button id="playlist-button" class="control-button"><i class="fas fa-list"></i></button>
        <div id="navigation-controls">
            <button id="prev-button" class="nav-button"><i class="fas fa-chevron-left"></i></button>
            <button id="next-button" class="nav-button"><i class="fas fa-chevron-right"></i></button>
        </div>
        <button id="change-background" class="control-button"><i class="fas fa-sync-alt"></i></button>
        <button id="toggle-info-panel"  class="control-button"><i class="fas fa-info-circle"></i></button>
        <button id="fullscreen-button" class="control-button"><i class="fas fa-expand"></i></button>
        <button id="open-schedule" class="control-button"><i class="fas fa-calendar"></i></button>
    </div>
</div>
  
<div id="playlist-popup">
    <button id="close-playlist">&times;</button>
    <h2><i class="fas fa-list-ul"></i> Gestion des Playlists</h2>
    
    <div class="playlist-columns">
        <!-- Colonne gauche : Playlist actuelle -->
        <div class="playlist-column">
            <div id="playlist-management">
                <div class="management-group">
                    <h3><i class="fas fa-plus-circle"></i> Ajouter un élément</h3>
                    <div id="add-item-form">
                        <select id="item-type">
                            <option value="image"><i class="fas fa-image"></i> Image</option>
                            <option value="video"><i class="fas fa-video"></i> Vidéo</option>
                            <option value="webpage"><i class="fas fa-globe"></i> Page Web</option>
                            <option value="local-webpage"><i class="fas fa-puzzle-piece"></i> Widget</option>
                            <option value="iframe"><i class="fas fa-code"></i> iFrame</option>
                        </select>
                        <input type="file" id="file-input" accept="image/*,video/*,text/html">
                        <input type="text" id="web-url-input" placeholder="URL de l'image ou de la vidéo">
                        <input type="text" id="webpage-url-input" placeholder="URL de la page web">
                        <textarea id="iframe-input" placeholder="Entrez le code iframe ici"></textarea>
                        <input type="text" id="iframe-name-input" placeholder="Nom de l'élément iFrame">
                        <div class="duration-container">
                            <label for="duration-input" id="duration-label">
                                <i class="fas fa-clock"></i> Durée (en secondes) :
                            </label>
                            <input type="number" id="duration-input" value="10" placeholder="Durée" min="1" step="1">
                        </div>
                        <button id="add-item-button" class="button-primary">
                            <i class="fas fa-plus"></i> Ajouter
                        </button>
                    </div>
                    <select id="playlist-select"></select>
                    <button id="load-playlist" class="button-primary">
                        <i class="fas fa-download"></i> Charger
                    </button>
                </div>

                <!-- Section Éditeurs Intégrés -->
                <div class="management-group editors-section">
                    <h3><i class="fas fa-magic"></i> Éditeurs Intégrés</h3>
                    <div class="editors-buttons">
                        <button id="open-meteo-editor-main" class="button-primary editor-btn meteo-btn">
                            <i class="fas fa-cloud-sun"></i>
                            <span>Créer Écran Météo</span>
                        </button>
                        <button id="open-info-editor-main" class="button-primary editor-btn info-btn">
                            <i class="fas fa-file-alt"></i>
                            <span>Créer Page Info</span>
                        </button>
                    </div>
                    <div class="editors-help">
                        <small><i class="fas fa-info-circle"></i> Créez du contenu personnalisé sans modifier de fichiers HTML</small>
                    </div>
                </div>

                <div class="management-group">
                    <input type="text" id="new-playlist-name" placeholder="Nom de la nouvelle playlist">
                    <div class="button-group-inline">
                        <button id="save-playlist" class="button-primary button-save">
                            <i class="fas fa-save"></i> Sauvegarder
                        </button>
                        <button id="delete-playlist" class="button-primary button-delete">
                            <i class="fas fa-trash-alt"></i> Supprimer
                        </button>
                        <button id="select-folder-button" class="button-primary">
                            <i class="fas fa-folder-open"></i> Dossier
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <!-- Colonne droite : Gestion des playlists -->
        <div class="playlist-column">
            <span id="playlist-label">
                <i class="fas fa-play-circle"></i> Playlist Actuelle
            </span>
            <ul id="playlist-items"></ul>
        </div>
    </div>
</div>


<div id="schedule-popup" class="modal">
    <div class="modal-content">
      <span class="close">&times;</span>
      <h2>Planification des Playlists</h2>
      
      <div id="schedule-form">
        <select id="playlist-schedule-select" required>
          <option value="">Sélectionnez une playlist</option>
        </select>
        
        <div class="time-inputs">
          <div>
            <label for="start-time">Heure de début:</label>
            <input type="time" id="start-time" required>
          </div>
          <div>
            <label for="end-time">Heure de fin:</label>
            <input type="time" id="end-time" required>
          </div>
        </div>
        
        <div class="days-selection">
          <label>Jours de diffusion:</label>
          <div class="days-checkboxes">
            <label><input type="checkbox" value="0"> Dim</label>
            <label><input type="checkbox" value="1"> Lun</label>
            <label><input type="checkbox" value="2"> Mar</label>
            <label><input type="checkbox" value="3"> Mer</label>
            <label><input type="checkbox" value="4"> Jeu</label>
            <label><input type="checkbox" value="5"> Ven</label>
            <label><input type="checkbox" value="6"> Sam</label>
          </div>
        </div>
        
        <button id="add-schedule" class="btn-primary">Ajouter la planification</button>
      </div>
      
      <div id="schedules-list">
        <h3>Planifications existantes</h3>
        <div id="schedules-container"></div>
      </div>
    </div>
  </div>
  
  <div id="notification"></div>

  <script src="maree.js"></script>
  <script src="meteo.js"></script>
  <script src="mareaDisplay.js"></script>
  <script src="planning.js"></script>

  <!-- Éditeurs Intégrés Nautiflix - Chargement avec vérification -->
  <script>
    console.log('Chargement des éditeurs Nautiflix...');
  </script>
  <script src="meteoEditor.js"></script>
  <script>
    console.log('MeteoEditor chargé:', typeof MeteoEditor !== 'undefined');
  </script>
  <script src="infoPageEditor.js"></script>
  <script>
    console.log('InfoPageEditor chargé:', typeof InfoPageEditor !== 'undefined');
  </script>
  <script src="editorsIntegration.js"></script>
  <script>
    console.log('Intégration des éditeurs chargée');
  </script>

  <!-- Script de débogage pour Electron -->
  <script src="debugEditors.js"></script>


  <script>
  let isPlaying = false;
  let currentItemIndex = 0;
  let currentPlaylist = [];
  let savedPlaylists = {};
  let currentTimer;
  let remainingTime;
  let defaultPlaylist = [];  // Pour stocker la playlist par défaut
  
  const backgroundImages = [
  './assets/Designer6.jpeg',
  './assets/Designer7.jpeg',
  './assets/windsurfer01.png',
  './assets/windsurfer2.png',
  // Ajoutez autant d'images que vous le souhaitez
];
  
  window.electronAPI.onMareesUpdated(() => {
    alert('Les données des marées ont été mises à jour. L\'application va se recharger.');
    window.location.reload();
  });
  
  // definir le dossier widgets
  document.getElementById('select-folder-button').addEventListener('click', async () => {
    try {
        // Prompt the user to select a folder.
        const folderHandle = await window.showDirectoryPicker();

        // Store the folder handle in localStorage.
        localStorage.setItem('localWebpageFolder', folderHandle.name);
        console.log('Selected folder:', folderHandle.name);

        // Display a notification to the user.
        showNotification(`Dossier "${folderHandle.name}" défini avec succès !`);
    } catch (error) {
        console.error('Error selecting folder:', error);
        showNotification('Erreur lors de la sélection du dossier.', 'error');
    }
});

function showNotification(message, type = 'success') {
    const notificationElement = document.getElementById('notification');
    notificationElement.textContent = message;
    notificationElement.style.display = 'block';
    notificationElement.style.backgroundColor = type === 'error' ? 'red' : 'green';
    setTimeout(() => {
        notificationElement.style.display = 'none';
    }, 3000);
}

// On page load, check if a folder is already defined.
document.addEventListener('DOMContentLoaded', () => {
    const savedFolder = localStorage.getItem('localWebpageFolder');
    if (savedFolder) {
        console.log('Saved folder:', savedFolder);
        showNotification(`Dossier "${savedFolder}" chargé.`);
    }
});

//fin definir le dossier widgets
    
  function changeBackgroundImage() {
      const standbyImage = document.getElementById('standby-image');
      const randomIndex = Math.floor(Math.random() * backgroundImages.length);
      standbyImage.style.backgroundImage = `url('${backgroundImages[randomIndex]}')`;
  }
  
  // Load saved playlists from localStorage
  if (localStorage.getItem('savedPlaylists')) {
      savedPlaylists = JSON.parse(localStorage.getItem('savedPlaylists'));
      updatePlaylistSelect();
  }

  function loadDefaultPlaylist() {
    const savedPlaylists = JSON.parse(localStorage.getItem('savedPlaylists') || '{}');
    if (savedPlaylists['default']) {
        defaultPlaylist = [...savedPlaylists['default']];
        return true;
    }
    return false;
}
  
  async function checkCurrentSchedule() {
    const now = new Date();
    const currentDay = now.getDay();
    const currentTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;

    try {
        const schedules = await window.electronAPI.getSchedules();
        const activeSchedule = schedules.find(schedule => {
            if (!schedule.enabled) return false;

            // Vérifier si le jour actuel est dans les jours planifiés
            if (!schedule.days.includes(currentDay)) return false;

            // Convertir les heures en minutes pour la comparaison
            const current = convertTimeToMinutes(currentTime);
            const start = convertTimeToMinutes(schedule.startTime);
            const end = convertTimeToMinutes(schedule.endTime);

            return current >= start && current <= end;
        });

        return activeSchedule;
    } catch (error) {
        console.error('Erreur lors de la vérification des planifications:', error);
        return null;
    }
}

// Fonction utilitaire pour convertir l'heure en minutes
function convertTimeToMinutes(timeString) {
    const [hours, minutes] = timeString.split(':').map(Number);
    return hours * 60 + minutes;
}

// Modifiez la fonction startStream existante
async function startStream() {
    if (!isPlaying) {
        // Vérifier s'il y a une planification active
        const activeSchedule = await checkCurrentSchedule();
        
        if (activeSchedule) {
            // Charger la playlist planifiée
            const savedPlaylists = JSON.parse(localStorage.getItem('savedPlaylists') || '{}');
            const plannedPlaylist = savedPlaylists[activeSchedule.playlistName];
            
            if (plannedPlaylist) {
                // Mettre à jour la playlist actuelle avec celle planifiée
                currentPlaylist = [...plannedPlaylist];
                currentItemIndex = 0;
                showNotification(`Lecture de la playlist planifiée : ${activeSchedule.playlistName}`);
            } else {
                showNotification('La playlist planifiée n\'est pas disponible', 'error');
                return;
            }
        }

        // Continuer seulement si nous avons une playlist (planifiée ou actuelle)
        if (currentPlaylist.length > 0) {
            isPlaying = true;
            document.getElementById('standby-image').style.display = 'none';
            playNextItem();
        } else {
            showNotification('Aucune playlist disponible pour la lecture', 'error');
        }
    }
}

// Ajoutez une fonction pour vérifier périodiquement les planifications
let scheduleCheckInterval;

// Fonction de vérification de la planification active avec gestion des changements
async function checkAndUpdateActiveSchedule() {
    const now = new Date();
    const currentDay = now.getDay();
    const currentTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
    const currentMinutes = convertTimeToMinutes(currentTime);

    try {
        // Charger la playlist par défaut si ce n'est pas déjà fait
        if (defaultPlaylist.length === 0) {
            loadDefaultPlaylist();
        }

        const schedules = await window.electronAPI.getSchedules();
        const activeSchedule = schedules.find(schedule => {
            if (!schedule.enabled) return false;
            if (!schedule.days.includes(currentDay)) return false;

            const start = convertTimeToMinutes(schedule.startTime);
            const end = convertTimeToMinutes(schedule.endTime);

            // Vérification précise du temps
            return currentMinutes >= start && currentMinutes < end; // Changé <= en < pour l'heure de fin
        });

        const savedPlaylists = JSON.parse(localStorage.getItem('savedPlaylists') || '{}');
        
        // Vérifier si une planification active se termine
        const endingSchedule = schedules.find(schedule => {
            if (!schedule.enabled) return false;
            if (!schedule.days.includes(currentDay)) return false;
            
            const end = convertTimeToMinutes(schedule.endTime);
            return currentMinutes === end;
        });

        if (endingSchedule && isPlaying) {
            console.log('Fin de plannification détectée');
            currentPlaylist = [...defaultPlaylist];
            currentItemIndex = 0;
            showNotification('Fin de programmation - Retour à la playlist par défaut');
            clearTimeout(currentTimer);
            playNextItem();
            return;
        }

        if (activeSchedule) {
            const plannedPlaylist = savedPlaylists[activeSchedule.playlistName];
            if (plannedPlaylist) {
                const currentPlaylistStr = JSON.stringify(currentPlaylist);
                const plannedPlaylistStr = JSON.stringify(plannedPlaylist);

                if (currentPlaylistStr !== plannedPlaylistStr) {
                    console.log('Changement vers playlist planifiée');
                    currentPlaylist = [...plannedPlaylist];
                    currentItemIndex = 0;
                    
                    if (isPlaying) {
                        showNotification(`Changement vers la playlist : ${activeSchedule.playlistName}`);
                        clearTimeout(currentTimer);
                        playNextItem();
                    }
                }
            }
        } else if (defaultPlaylist.length > 0) {
            // Vérifier si nous ne sommes pas déjà sur la playlist par défaut
            const currentPlaylistStr = JSON.stringify(currentPlaylist);
            const defaultPlaylistStr = JSON.stringify(defaultPlaylist);

            if (currentPlaylistStr !== defaultPlaylistStr) {
                console.log('Changement vers playlist par défaut');
                currentPlaylist = [...defaultPlaylist];
                currentItemIndex = 0;
                
                if (isPlaying) {
                    showNotification('Passage à la playlist par défaut');
                    clearTimeout(currentTimer);
                    playNextItem();
                }
            }
        }
    } catch (error) {
        console.error('Erreur lors de la vérification des planifications:', error);
    }
}

// Modifions également la fréquence de vérification pour être plus réactif
function startScheduleCheck() {
    // Première vérification immédiate
    checkAndUpdateActiveSchedule();
    
    // Vérification toutes les 20 secondes pour être plus précis
    scheduleCheckInterval = setInterval(() => {
        checkAndUpdateActiveSchedule();
    }, 20000); // 20 secondes
}

// Modifiez également la fonction startScheduleCheck pour une vérification plus fréquente
function startScheduleCheck() {
    // Première vérification immédiate
    checkAndUpdateActiveSchedule();
    
    // Vérification toutes les 30 secondes au lieu d'une minute
    scheduleCheckInterval = setInterval(() => {
        checkAndUpdateActiveSchedule();
    }, 30000); // 30 secondes
}

// Démarrage de la vérification périodique
function startScheduleCheck() {
    // Première vérification immédiate
    checkAndUpdateActiveSchedule();
    
    // Vérification toutes les minutes
    scheduleCheckInterval = setInterval(() => {
        checkAndUpdateActiveSchedule();
    }, 60000);
}

// Modification de la fonction startStream
async function startStream() {
    if (!isPlaying) {
        // Charger la playlist par défaut si pas déjà fait
        if (defaultPlaylist.length === 0) {
            loadDefaultPlaylist();
        }

        // Vérifier la planification active
        const activeSchedule = await checkCurrentSchedule();
        
        if (activeSchedule) {
            // Charger la playlist planifiée
            const savedPlaylists = JSON.parse(localStorage.getItem('savedPlaylists') || '{}');
            const plannedPlaylist = savedPlaylists[activeSchedule.playlistName];
            
            if (plannedPlaylist) {
                currentPlaylist = [...plannedPlaylist];
                currentItemIndex = 0;
                showNotification(`Lecture de la playlist planifiée : ${activeSchedule.playlistName}`);
            } else if (defaultPlaylist.length > 0) {
                // Si la playlist planifiée n'est pas disponible, utiliser celle par défaut
                currentPlaylist = [...defaultPlaylist];
                currentItemIndex = 0;
                showNotification('Utilisation de la playlist par défaut');
            }
        } else if (defaultPlaylist.length > 0) {
            // Si pas de planification active, utiliser la playlist par défaut
            currentPlaylist = [...defaultPlaylist];
            currentItemIndex = 0;
            showNotification('Utilisation de la playlist par défaut');
        }

        if (currentPlaylist.length > 0) {
            isPlaying = true;
            document.getElementById('standby-image').style.display = 'none';
            startScheduleCheck();
            playNextItem();
        } else {
            showNotification('Aucune playlist disponible pour la lecture', 'error');
        }
    }
}

// Modification de la fonction resetStream
function resetStream() {
    isPlaying = false;
    currentItemIndex = 0;
    clearTimeout(currentTimer);
    
    // Arrêter la vérification des planifications
    if (scheduleCheckInterval) {
        clearInterval(scheduleCheckInterval);
        scheduleCheckInterval = null;
    }

    const streamContent1 = document.getElementById('stream-content-1');
    const streamContent2 = document.getElementById('stream-content-2');
    
    streamContent1.innerHTML = '';
    streamContent2.innerHTML = '';
    streamContent1.style.opacity = '0';
    streamContent2.style.opacity = '0';
    
    document.getElementById('standby-image').style.display = 'block';
    document.getElementById('timer').textContent = '';
    
    showNotification('Le flux a été réinitialisé');
}

// Modification de playNextItem pour vérifier la planification
function playNextItem() {
    clearTimeout(currentTimer);
    
    // Si on atteint la fin de la playlist, revenir au début
    if (currentItemIndex >= currentPlaylist.length) {
        currentItemIndex = 0;
        // Vérifier la planification avant de redémarrer la playlist
        checkAndUpdateActiveSchedule();
    }
    
    const item = currentPlaylist[currentItemIndex];
    if (item) {
        playItem(currentPlaylist[currentItemIndex]);
        currentItemIndex++;
    }
}
  function playPreviousItem() {
      clearTimeout(currentTimer);
      currentItemIndex = (currentItemIndex - 2 + currentPlaylist.length) % currentPlaylist.length;
      playNextItem();
  }
  
  function playItem(item) {
      const currentContent = document.getElementById('stream-content-1');
      const nextContent = document.getElementById('stream-content-2');
      
      nextContent.innerHTML = '';
      
      if (item.type === 'image') {
          const img = document.createElement('img');
          img.src = item.content;
          img.alt = "Image du flux nautique";
          img.style.maxWidth = '100%';
          img.style.maxHeight = '100%';
          nextContent.appendChild(img);
          startTimer(item.duration);
      } else if (item.type === 'video') {
          const video = document.createElement('video');
          video.src = item.content;
          video.autoplay = true;
          video.muted = false; // Permettre l'audio par défaut
          video.controls = true; // Ajouter les contrôles vidéo
          video.style.maxWidth = '100%';
          video.style.maxHeight = '100%';
          video.onloadedmetadata = function() {
              startTimer(video.duration * 1000);
          };
          video.onended = playNextItem;
          nextContent.appendChild(video);
      } else if (item.type === 'webpage' ||  item.type === 'local-webpage') {
          const iframe = document.createElement('iframe');
          iframe.src = item.content;
          iframe.style.width = '100%';
          iframe.style.height = '100%';
          iframe.style.border = 'none';
          nextContent.appendChild(iframe);
          startTimer(item.duration);
      } else if (item.type === 'iframe') {
          nextContent.innerHTML = item.content;
          startTimer(item.duration);
      } else if (item.type === 'meteo-custom') {
          // Contenu météo créé par l'éditeur intégré
          nextContent.innerHTML = item.content;
          nextContent.classList.add('meteo-display');
          startTimer(item.duration);
      } else if (item.type === 'info-page') {
          // Page d'information créée par l'éditeur intégré
          nextContent.innerHTML = item.content;
          nextContent.classList.add('info-page-display');
          startTimer(item.duration);
      }
      
      currentContent.style.opacity = '0';
      nextContent.style.opacity = '1';
      
      setTimeout(() => {
          const temp = currentContent.id;
          currentContent.id = nextContent.id;
          nextContent.id = temp;
      }, 1000);
  }
  
  function startTimer(duration) {
      const timerElement = document.getElementById('timer');
      remainingTime = duration;
      
      function updateTimer() {
          const minutes = Math.floor(remainingTime / 60000);
          const seconds = ((remainingTime % 60000) / 1000).toFixed(0);
          timerElement.textContent = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
          
          if (remainingTime > 0) {
              remainingTime -= 1000;
              currentTimer = setTimeout(updateTimer, 1000);
          } else {
              playNextItem();
          }
      }
      
      updateTimer();
  }
  
  function togglePlaylistPopup() {
      const popup = document.getElementById('playlist-popup');
      if (popup.style.display === 'none' || popup.style.display === '') {
          popup.style.display = 'block';
          updatePlaylistDisplay();
      } else {
          popup.style.display = 'none';
      }
  }
  
  
  function updatePlaylistDisplay() {
    const playlistElement = document.getElementById('playlist-items');
    playlistElement.innerHTML = '';

    // Icônes pour chaque type d'élément
    const typeIcons = {
        'image': 'fa-image',
        'video': 'fa-video',
        'webpage': 'fa-globe',
        'local-webpage': 'fa-puzzle-piece',
        'iframe': 'fa-code'
    };

    currentPlaylist.forEach((item, index) => {
        const li = document.createElement('li');
        // Ajoute une icône en fonction du type
        const itemIcon = typeIcons[item.type] || 'fa-file';
        const itemName = `
            <i class="fas ${itemIcon}"></i>
            ${item.originalName || 'Sans nom'}
        `;

        li.innerHTML = `
            <div class="playlist-item-content">
                <span class="item-type">${itemName}</span>
                ${item.type !== 'video' ? `
                    <div class="duration-group">
                        <i class="fas fa-clock"></i>
                        <input type="number" 
                               class="duration-input" 
                               value="${item.duration / 1000}" 
                               min="1" 
                               step="1"
                               title="Durée en secondes">
                    </div>
                ` : ''}
            </div>
            <div class="playlist-item-controls">
                ${item.type !== 'video' ? `
                    <button class="button-primary button-save save-duration" title="Sauvegarder la durée">
                        <i class="fas fa-save"></i>
                    </button>
                ` : ''}
                <div class="move-buttons">
                    <button class="button-primary move-button up" title="Déplacer vers le haut">
                        <i class="fas fa-chevron-up"></i>
                    </button>
                    <button class="button-primary move-button down" title="Déplacer vers le bas">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
                <button class="button-primary button-delete delete-button" title="Supprimer">
                    <i class="fas fa-trash-alt"></i>
                </button>
                <button class="button-primary button-duplicate duplicate-button" title="Dupliquer">
                    <i class="fas fa-copy"></i>
                </button>
            </div>
        `;
        
        playlistElement.appendChild(li);

        // Ajout des écouteurs d'événements avec feedback visuel
        const addButtonListener = (selector, callback) => {
            const button = li.querySelector(selector);
            if (button) {
                button.addEventListener('click', async (e) => {
                    e.preventDefault();
                    button.classList.add('button-active');
                    await callback();
                    button.classList.remove('button-active');
                });
            }
        };

        // Application des écouteurs avec retour visuel
        if (item.type !== 'video') {
            addButtonListener('.save-duration', () => saveDuration(index));
        }
        addButtonListener('.move-button.up', () => moveItem(index, -1));
        addButtonListener('.move-button.down', () => moveItem(index, 1));
        addButtonListener('.delete-button', () => {
            if (confirm('Êtes-vous sûr de vouloir supprimer cet élément ?')) {
                deleteItem(index);
            }
        });
        addButtonListener('.duplicate-button', () => duplicateItem(index));

        // Ajout d'info-bulles dynamiques
        const durationInput = li.querySelector('.duration-input');
        if (durationInput) {
            durationInput.addEventListener('input', function() {
                this.setAttribute('title', `Durée actuelle : ${this.value} secondes`);
            });
        }
    });
}

function duplicateItem(index) {
    try {
        const item = currentPlaylist[index];
        const newItem = JSON.parse(JSON.stringify(item));
        
        // Ajouter un suffixe au nom pour indiquer que c'est une copie
        if (newItem.originalName) {
            newItem.originalName = `${newItem.originalName} (copie)`;
        }
        
        currentPlaylist.splice(index + 1, 0, newItem);
        updatePlaylistDisplay();
        savePlaylistsToLocalStorage();
        
        // Notification de succès
        showNotification('Élément dupliqué avec succès', 'success');
    } catch (error) {
        console.error('Erreur lors de la duplication:', error);
        showNotification('Erreur lors de la duplication de l\'élément', 'error');
    }
}

// Fonction utilitaire pour les notifications
function showNotification(message, type = 'info') {
    const notification = document.getElementById('notification');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    notification.style.display = 'block';
    
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
            notification.style.display = 'none';
            notification.style.opacity = '1';
        }, 300);
    }, 2000);
}
  
  function resetStream() {
      isPlaying = false;
      currentItemIndex = 0;
      clearTimeout(currentTimer);
      const streamContent1 = document.getElementById('stream-content-1');
      const streamContent2 = document.getElementById('stream-content-2');
      
      streamContent1.innerHTML = '';
      streamContent2.innerHTML = '';
      streamContent1.style.opacity = '0';
      streamContent2.style.opacity = '0';
      
      document.getElementById('standby-image').style.display = 'block';
      document.getElementById('timer').textContent = '';
      
      showNotification('Le flux a été réinitialisé. Cliquez sur "Démarrer le flux" pour recommencer.');
  }
  
  function loadPlaylist() {
      const name = document.getElementById('playlist-select').value;
      if (name && savedPlaylists[name]) {
          currentPlaylist = JSON.parse(JSON.stringify(savedPlaylists[name]));
          currentPlaylist.forEach(item => {
              if (item.type === 'video' && item.content.startsWith('data:video')) {
                  item.content = item.content; // Base64 data URL already
              }
          });
          currentItemIndex = 0; // Réinitialiser l'index de l'élément actuel
          updatePlaylistDisplay();
          showNotification(`Playlist "${name}" chargée avec succès !`);
      } else {
          showNotification('Veuillez sélectionner une playlist valide.', 'error');
      }
  }

  
  
  // Ajouter un élément à la playlist
  function addItemToPlaylist() {
  const type = document.getElementById('item-type').value;
  const fileInput = document.getElementById('file-input');
  const webUrlInput = document.getElementById('web-url-input');
  const webpageUrlInput = document.getElementById('webpage-url-input');
  const iframeInput = document.getElementById('iframe-input');
  const iframeNameInput = document.getElementById('iframe-name-input');
  const durationInput = document.getElementById('duration-input');

  let content, duration, name;

  if (type === 'webpage') {
    content = webpageUrlInput.value;
    originalName = new URL(content).hostname;
  } else if (type === 'iframe') {
    content = iframeInput.value;
    name = iframeNameInput.value;
    originalName = name;
  } else if (type === 'image' || type === 'video' || type === 'local-webpage') {
    if (webUrlInput.value) {
      content = webUrlInput.value;
      originalName = content.split('/').pop();
    } else if (fileInput.files.length > 0) {
      const file = fileInput.files[0];
      originalName = file.name;
      const reader = new FileReader();
      reader.onloadend = function() {
        content = reader.result;
        addItem(content, type, name, durationInput, originalName);
      };
      reader.readAsDataURL(file);
      return;
    } else {
      showNotification('Veuillez sélectionner un fichier ou entrer une URL.', 'error');
      return;
    }
  } else {
    showNotification('Type d\'élément inconnu.', 'error');
    return;
  }

  if (type === 'local-webpage') {
    const widgetsFolderPath = path.join(app.getPath('userData'), 'widgets');
    content = `${widgetsFolderPath}/${originalName}`;
  }

  addItem(content, type, name, durationInput, originalName);
}

function addItem(content, type, name, durationInput, originalName) {
    let duration = parseInt(durationInput.value) * 1000; // Convert to milliseconds

    if (!content || (type !== 'video' && isNaN(duration)) || (type === 'iframe' && !name)) {
        showNotification('Veuillez remplir tous les champs.', 'error');
        return;
    }

    if (type === 'video') {
        const video = document.createElement('video');
        video.src = content;
        video.onloadedmetadata = function() {
            duration = video.duration * 1000; // Video duration in milliseconds
            const newItem = { type, content, duration, originalName };
            currentPlaylist.push(newItem);
            updatePlaylistDisplay();
            showNotification('Élément vidéo ajouté à la playlist avec succès !');
            savePlaylistsToLocalStorage();
        };
    } else {
        const newItem = { type, content, duration, originalName };
        if (type === 'iframe') {
            newItem.name = name;
        }
        currentPlaylist.push(newItem);
        updatePlaylistDisplay();
        showNotification('Élément ajouté à la playlist avec succès !');
        savePlaylistsToLocalStorage();
    }

    // Reset form
    document.getElementById('file-input').value = '';
    document.getElementById('web-url-input').value = '';
    document.getElementById('webpage-url-input').value = '';
    document.getElementById('iframe-input').value = '';
    document.getElementById('iframe-name-input').value = '';
    durationInput.value = '10';
}

function toggleInputFields() {
      const type = document.getElementById('item-type').value;
      const fileInput = document.getElementById('file-input');
      const webUrlInput = document.getElementById('web-url-input');
      const webpageUrlInput = document.getElementById('webpage-url-input');
      const iframeInput = document.getElementById('iframe-input');
      const iframeNameInput = document.getElementById('iframe-name-input');
      const durationContainer = document.querySelector('.duration-container');
      
      fileInput.style.display = (type === 'image' || type === 'video' || 'local-webpage') ? 'block' : 'none';
      webUrlInput.style.display = (type === 'image' || type === 'video') ? 'block' : 'none';
      webpageUrlInput.style.display = (type === 'webpage') ? 'block' : 'none';
      iframeInput.style.display = (type === 'iframe') ? 'block' : 'none';
      iframeNameInput.style.display = (type === 'iframe') ? 'block' : 'none';
      durationContainer.style.display = (type !== 'video') ? 'flex' : 'none';
  }

  document.getElementById('item-type').addEventListener('change', function() {
  const type = this.value;
  if (type === 'local-webpage') {
    document.getElementById('file-input').value = '';
    document.getElementById('web-url-input').value = '';
    document.getElementById('webpage-url-input').value = '';
    document.getElementById('iframe-input').value = '';
    document.getElementById('iframe-name-input').value = '';
  }
});
  
  function moveItem(index, direction) {
        if (index + direction >= 0 && index + direction < currentPlaylist.length) {
          const item = currentPlaylist.splice(index, 1)[0];
          currentPlaylist.splice(index + direction, 0, item);
          updatePlaylistDisplay();
        }
      }
  
  function deleteItem(index) {
      currentPlaylist.splice(index, 1);
      updatePlaylistDisplay();
      showNotification('Élément supprimé de la playlist.');
      savePlaylistsToLocalStorage();
  }


function saveDuration(index) {
    // Trouver l'élément li parent correspondant à cet index
    const playlistItems = document.querySelectorAll('#playlist-items > li');
    if (index < 0 || index >= playlistItems.length) {
        console.error(`Index invalide : ${index}`);
        showNotification('Erreur : Élément de playlist non trouvé.', 'error');
        return;
    }

    const listItem = playlistItems[index];
    const durationInput = listItem.querySelector('.duration-input');

    if (!durationInput) {
        console.error(`Élément de saisie de durée non trouvé pour l'index ${index}`);
        showNotification('Erreur : Champ de durée non trouvé.', 'error');
        return;
    }

    const newDurationSeconds = parseFloat(durationInput.value);
    if (isNaN(newDurationSeconds) || newDurationSeconds <= 0) {
        showNotification('Veuillez entrer une durée valide (nombre positif).', 'error');
        return;
    }

    const newDurationMilliseconds = newDurationSeconds * 1000;
    currentPlaylist[index].duration = newDurationMilliseconds;

    showNotification('Durée mise à jour !');
    savePlaylistsToLocalStorage();
}

  
  function updatePlaylistSelect() {
      const select = document.getElementById('playlist-select');
      select.innerHTML = '';
      Object.keys(savedPlaylists).forEach(name => {
          const option = document.createElement('option');
          option.value = name;
          option.textContent = name;
          select.appendChild(option);
      });
  }
  
  
  function savePlaylist() {
      const name = document.getElementById('new-playlist-name').value.trim();
      if (name) {
          savedPlaylists[name] = JSON.parse(JSON.stringify(currentPlaylist));
          savePlaylistsToLocalStorage();
          updatePlaylistSelect();
          showNotification(`Playlist "${name}" sauvegardée avec succès !`);
      } else {
          showNotification('Veuillez entrer un nom pour la nouvelle playlist.', 'error');
      }
  }
  
  function deletePlaylist() {
      const name = document.getElementById('playlist-select').value;
      if (name && savedPlaylists[name]) {
          delete savedPlaylists[name];
          savePlaylistsToLocalStorage();
          updatePlaylistSelect();
          showNotification(`Playlist "${name}" supprimée avec succès !`);
      } else {
          showNotification('Veuillez sélectionner une playlist valide à supprimer.', 'error');
      }
  }
  
  function savePlaylistsToLocalStorage() {
      localStorage.setItem('savedPlaylists', JSON.stringify(savedPlaylists));
  }
  
  function showNotification(message, type = 'success') {
      const notification = document.getElementById('notification');
      notification.textContent = message;
      notification.style.backgroundColor = type === 'success' ? '#4CAF50' : '#f44336';
      notification.style.display = 'block';
      notification.style.opacity = '1';
      
      setTimeout(() => {
          notification.style.opacity = '0';
          setTimeout(() => {
              notification.style.display = 'none';
          }, 300);
      }, 3000);
  }
  
  function toggleFullscreen() {
      if (!document.fullscreenElement) {
          document.documentElement.requestFullscreen().catch(err => {
              showNotification(`Erreur lors du passage en plein écran : ${err.message}`, 'error');
          });
      } else {
          if (document.exitFullscreen) {
              document.exitFullscreen();
          }
      }
  }
  
  // Récupérer les éléments nécessaires
  const infoPanel = document.getElementById('info-panel');
  const toggleButton = document.getElementById('toggle-info-panel');
  
  // Afficher le panneau d'informations au démarrage
  infoPanel.classList.add('active');
  toggleButton.textContent = 'Infos';
  toggleButton.classList.add('active');
  
  function toggleInfoPanel() {
    const infoPanel = document.getElementById('info-panel');
    const toggleButton = document.getElementById('toggle-info-panel');
    if (infoPanel.classList.contains('active')) {
        infoPanel.classList.remove('active');
        toggleButton.innerHTML = '<i class="fas fa-on"></i> On';
        toggleButton.classList.remove('active');
    } else {
        infoPanel.classList.add('active');
        toggleButton.innerHTML = '<i class="fas fa-off"></i> Off';
        toggleButton.classList.add('active');
    }
}
  
  // Event listeners
  document.getElementById('start-stream').addEventListener('click', startStream);
  document.getElementById('prev-button').addEventListener('click', playPreviousItem);
  document.getElementById('next-button').addEventListener('click', playNextItem);
  document.getElementById('playlist-button').addEventListener('click',togglePlaylistPopup);
  document.getElementById('reset-stream').addEventListener('click', resetStream);
  document.getElementById('close-playlist').addEventListener('click', togglePlaylistPopup);
  document.getElementById('add-item-button').addEventListener('click', addItemToPlaylist);
  document.getElementById('item-type').addEventListener('change', toggleInputFields);
  document.getElementById('load-playlist').addEventListener('click', loadPlaylist);
  document.getElementById('save-playlist').addEventListener('click', savePlaylist);
  document.getElementById('delete-playlist').addEventListener('click', deletePlaylist);
  document.getElementById('fullscreen-button').addEventListener('click', toggleFullscreen);
  document.getElementById('toggle-info-panel').addEventListener('click', toggleInfoPanel);
  document.getElementById('change-background').addEventListener('click', changeBackgroundImage);

  // Événements pour les éditeurs intégrés - avec vérification Electron
  function initializeEditors() {
      const meteoBtn = document.getElementById('open-meteo-editor-main');
      const infoBtn = document.getElementById('open-info-editor-main');

      if (meteoBtn) {
          meteoBtn.addEventListener('click', function() {
              console.log('Bouton météo cliqué');
              openMeteoEditorSafe();
          });
      }

      if (infoBtn) {
          infoBtn.addEventListener('click', function() {
              console.log('Bouton info cliqué');
              openInfoEditorSafe();
          });
      }
  }

  function openMeteoEditorSafe() {
      try {
          if (typeof MeteoEditor !== 'undefined') {
              const editor = new MeteoEditor();
              const editorHTML = editor.createEditor();

              const editorContainer = document.createElement('div');
              editorContainer.innerHTML = editorHTML;
              document.body.appendChild(editorContainer.firstElementChild);

              editor.initializeEditor();
              console.log('Éditeur météo ouvert avec succès');
          } else {
              console.error('MeteoEditor non défini');
              alert('Erreur: Éditeur météo non disponible');
          }
      } catch (error) {
          console.error('Erreur lors de l\'ouverture de l\'éditeur météo:', error);
          alert('Erreur lors de l\'ouverture de l\'éditeur météo: ' + error.message);
      }
  }

  function openInfoEditorSafe() {
      try {
          if (typeof InfoPageEditor !== 'undefined') {
              const editor = new InfoPageEditor();
              const editorHTML = editor.createEditor();

              const editorContainer = document.createElement('div');
              editorContainer.innerHTML = editorHTML;
              document.body.appendChild(editorContainer.firstElementChild);

              editor.initializeEditor();
              console.log('Éditeur de pages ouvert avec succès');
          } else {
              console.error('InfoPageEditor non défini');
              alert('Erreur: Éditeur de pages non disponible');
          }
      } catch (error) {
          console.error('Erreur lors de l\'ouverture de l\'éditeur de pages:', error);
          alert('Erreur lors de l\'ouverture de l\'éditeur de pages: ' + error.message);
      }
  }

  // Initialiser les éditeurs après un délai pour s'assurer que tout est chargé
  setTimeout(function() {
      console.log('Initialisation des éditeurs...');
      initializeEditors();

      // Vérifier que les boutons existent
      const meteoBtn = document.getElementById('open-meteo-editor-main');
      const infoBtn = document.getElementById('open-info-editor-main');

      console.log('Bouton météo trouvé:', meteoBtn !== null);
      console.log('Bouton info trouvé:', infoBtn !== null);

      if (!meteoBtn || !infoBtn) {
          console.warn('Boutons des éditeurs non trouvés. Vérifiez que la popup de playlist est bien générée.');
      }
  }, 2000);
  
  document.getElementById('playlist-items').addEventListener('click', (e) => {
      if (e.target.classList.contains('delete-item')) {
          deleteItem(parseInt(e.target.dataset.index));
      } else if (e.target.classList.contains('save-duration')) {
          saveDuration(parseInt(e.target.dataset.index));
      }
  });
  
  // Initial setup
  toggleInputFields();
  updatePlaylistSelect();
  updateDateTime();
  updateWeather();
  changeBackgroundImage();
  loadPlaylist()
  </script>
  </body>
  </html>